# Documentação Modelo Preditivo - Inteli

## HotData

### Red Hot Chilli Peppers

#### <PERSON>, <PERSON><PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>, <PERSON>  

## <PERSON><PERSON><PERSON><PERSON>

<details>
  <summary><a href="#c1">1. Introdução</a></summary>
</details>

<details>
  <summary><a href="#c2">2. Objetivos e Justificativa</a></summary>
  <ul>
    <li><a href="#c2.1">2.1. Objetivos</a></li>
    <li><a href="#c2.2">2.2. Proposta de solução</a></li>
    <li><a href="#c2.3">2.3.Justificativa</a></li>
  </ul>
</details>

<details>
  <summary><a href="#c3">3. Metodologia</a></summary>
</details>

<details>
<summary><a href="#c4">4. Desenvolvimento e Resultados</a></summary>
  <ul>
    <li><a href="#c4.1">4.1.Compreensão do Problema</a></li>
      <ul>
        <li><a href="#c4.1.1">4.1.1. Contexto da indústria</a></li>
        <li><a href="#c4.1.2">4.1.2. Análise SWOT</a></li>
        <li><a href="#c4.1.3">4.1.3. Planejamento Geral da Solução</a></li>
        <li><a href="#c4.1.4">4.1.4. Value Proposition Canvas</a></li>
        <li><a href="#c4.1.5">4.1.5. Matriz de Riscos</a></li>
        <li><a href="#c4.1.6">4.1.6. Personas</a></li>
        <li><a href="#c4.1.7">4.1.7. Jornadas do Usuário</a></li>
        <li><a href="#c4.1.8">4.1.8. Política de Privacidade</a></li>
      </ul>
    <li><a href="#c4.2">4.2. Compreensão dos Dados</a></li>
      <ul>
        <li><a href="#c4.2.1">4.2.1. Exploração de dados</a>
        </li>
        <li><a href="#c4.2.2">4.2.2. Pré-processamento dos dados</a></li>
        <li><a href="#c4.2.3">4.2.3. Hipóteses</a></li>
      </ul>
    <summary><li><a href="#c4.3">4.3. Preparação dos Dados e Modelagem</a></li></summary>
      <ul>
        <li><a href="#c4.3.1">4.3.1 Visão Geral dos Dois Modelos Híbridos</a></li>
        <li><a href="#c4.3.2">4.3.2 Fundamentos Técnicos Compartilhados</a></li>
        <li><a href="#c4.3.3">4.3.3 Modelo Híbrido 1 (Clientes → Investimento)</a></li>
          <ul>
            <li><a href="#c*******">******* Modelagem do Problema - Aprendizado Não Supervisionado</a></li>
            <li><a href="#c*******">******* Clusterização de Clientes e Definição de K</a></li>
            <li><a href="#c*******">******* Caracterização dos Clusters Identificados</a></li>
            <li><a href="#c*******">******* Modelo Supervisionado (Score de Potencial Grau)</a></li>
            <li><a href="#c*******">******* Validação da abordagem híbrida (clustering + classificação)</a></li>
            <li><a href="#c*******">******* Módulo de Recomendação e Ranqueamento</a></li>
            <li><a href="#c*******">******* Limitações e Conclusões</a></li>
          </ul>
        <li><a href="#c4.3.4">4.3.4 Modelo Híbrido 2 (Cidades → Expansão de Óticas)</a></li>
          <ul>
            <li><a href="#c*******">******* Modelagem do Problema - Aprendizado Não Supervisionado</a></li>
            <li><a href="#c*******">******* Definição de K e Clusterização (Cidades)</a></li>
            <li><a href="#c*******">******* Caracterização dos Clusters Identificados</a></li>
            <li><a href="#c*******">******* Modelo Supervisionado (Performance Score)</a></li>
            <li><a href="#c*******">******* Sistema de Recomendação de Expansão</a></li>
            <li><a href="#c4.3.4.6">4.3.4.6 Limitações e Conclusões</a></li>
          </ul>
      </ul>
    <li><a href="#c4.4">4.4. Comparação de Modelos</a></li>
    <li><a href="#c4.5">4.5. Avaliação</a></li>
  </ul>
</details>

<details>
  <summary><a href="#c5">5. Conclusões e Recomendações</a></summary>
</details>

<details>
  <summary><a href="#c6">6. Referências</a></summary>
</details>

<details>
<summary>Anexos</a></summary>
</details>

<br>

## <a name="c1"></a>1. Introdução

&ensp;A Chilli Beans é uma empresa brasileira de grande porte, a qual consolidou-se, ao longo de seus mais de 28 anos de atuação, como a maior rede especializada em óculos e acessórios da América Latina. Com mais de 900 pontos de venda espalhados pelo Brasil, além de presença internacional em mais de vinte países. A empresa destaca-se não apenas pelo seu crescimento exponencial, mas também por sua presença marcante no mercado de moda e estilo de vida, com produtos que vão desde óculos de sol e óculos de grau até relógios e acessórios diversos (CHILLI BEANS, 2025).

&ensp;Em território nacional, a empresa ocupa o terceiro lugar entre as maiores redes de óticas, mantendo a liderança no segmento de óculos de sol. Para 2024, sua receita total está projetada em R$ 1,2 bilhão, o que a posiciona como uma referência em inovação, branding e expansão estratégica (FORBES, 2024). A empresa possui três vertentes principais: Chilli Beans, Óticas Chilli Beans e Eco Chilli, sendo cada uma direcionada a diferentes perfis e experiências de consumo. A marca também é reconhecida por seu apelo jovem, ousado e irreverente, utilizando estratégias de colaboração com empresas e temáticas como NASA, Disney, Volkswagen e Risqué, em coleções que visam agregar valor emocional e simbólico aos seus produtos (MYBEST, 2025).

&ensp;Apesar do seu posicionamento consolidado no mercado, a empresa enfrenta desafios relevantes que demandam soluções tecnológicas e preditivas. Dentre os principais, destacam-se: (1) o baixo fluxo e conversão nas unidades físicas de rua da Ótica Chilli Beans, modelo ainda recente e pouco familiar ao público; (2) a necessidade de definir com precisão o perfil ideal de cliente para cada uma das marcas, com vistas a orientar campanhas de comunicação personalizadas e aumentar a assertividade na captação de novos consumidores; e (3) a baixa penetração da categoria de óculos de grau, que apesar da força das armações da marca, ainda não se consolidou como referência nesse nicho. (TAPI CHILLI BEANS 2025)

Fontes:
<sub>TAPI CHILLI BEANS. Material institucional disponibilizado para o projeto preditivo 2025. Inteli, 2025. Acesso em: 07 ago. 2025.</sub>
<sub>FORBES. How Chilli Beans Innovates at the Intersection of Fashion and Tech. 2024. Disponível em: https://www.forbes.com/sites/angelicamarideoliveira/2024/09/02/how-chilli-beans-innovates-at-the-intersection-of-fashion-and-tech/. Acesso em: 07 ago. 2025.</sub>
<sub>MYBEST. Melhores marcas de óculos de sol para comprar em 2025. 2025. Disponível em: https://www.mybest.com.br/14473. Acesso em: 07 ago. 2025.</sub>

## <a name="c2"></a>2. Objetivos e Justificativa

### <a name="c2.1"></a>2.1. Objetivos

```
Descreva resumidamente os objetivos gerais e específicos do seu parceiro de negócios.

Remova este bloco ao final
```

### <a name="c2.2"></a>2.2. Proposta de solução

```
Descreva resumidamente sua proposta de modelo preditivo e como esse modelo pretende resolver o problema, atendendo os objetivos.

Remova este bloco ao final
```

### <a name="c2.3"></a>2.3. Justificativa

```
Faça uma breve defesa de sua proposta de solução, escreva sobre seus potenciais, seus benefícios e como ela se diferencia.

Remova este bloco ao final
```

## <a name="c3"></a>3. Metodologia

&ensp; A metodologia adotada para o desenvolvimento do modelo preditivo foi a CRISP-DM (Cross Industry Standard Process for Data Mining), uma metodologia amplamente reconhecida e adotada por empresas de todo o mundo. A CRISP-DM é uma metodologia ágil e flexível, que permite uma abordagem sistemática e estruturada para projetos de mineração de dados, garantindo que todos os aspectos do projeto sejam considerados de forma integrada.

### <a name="c3.1"></a>3.1. Introdução CRISP-DM

&ensp; CRISP-DM, que significa Cross Industry Standard Process for Data Mining (Processo Padrão Inter-Indústrias para Mineração de Dados), é uma metodologia ágil e amplamente reconhecida que oferece uma abordagem estruturada e robusta para o planejamento e execução de projetos que envolvem mineração de dados, Machine Learning e análise de dados. Criada em 1996 pela IBM, a metodologia surgiu da necessidade de um processo padronizado que pudesse lidar com o crescente volume e complexidade dos dados, algo que as metodologias existentes na época não conseguiam suprir de forma eficiente.

&ensp; O CRISP-DM destaca-se por ser cíclico e iterativo, permitindo às equipes explorar e refinar abordagens conforme surgem novas informações e desafios, retornar a etapas anteriores sem reiniciar o projeto e assim manter alinhamento com os objetivos de negócio e as características dos dados; além disso, enfatiza o entendimento do negócio e a colaboração entre stakeholders para garantir que os resultados sejam aplicáveis e gerem valor real.

&ensp;Segundo Chapman et al. (2000), o CRISP-DM é amplamente utilizado devido à sua flexibilidade e aplicabilidade em diferentes setores. A metodologia foi projetada para ser independente de ferramentas e domínios, permitindo que equipes de ciência de dados adaptem suas etapas a projetos de diferentes complexidades. Sua abordagem iterativa é especialmente útil em cenários onde os dados são dinâmicos e os objetivos de negócio podem evoluir ao longo do tempo.

### <a name="c3.2"></a>3.2. Como a metodologia CRISP-DM é aplicada

&ensp; O CRISP-DM opera como um processo cíclico, o que é fundamental para projetos que lidam com grandes volumes de dados, informações e variáveis. A natureza cíclica reconhece que, no início de um projeto de dados, é quase impossível ter uma percepção completa de todos os resultados e desafios que surgirão. Portanto, a metodologia permite explorações passo a passo, com a capacidade de retornar a estágios anteriores sempre que necessário para refinar o entendimento ou a abordagem.

&ensp; A metodologia também pode ser combinada com outras abordagens ágeis, como o SCRUM, para otimizar ainda mais o fluxo de trabalho e a entrega incremental de valor. Essa flexibilidade garante que o CRISP-DM seja uma ferramenta adaptável e eficaz para uma vasta gama de projetos de dados, desde a mineração tradicional até aplicações mais complexas de Machine Learning.

### <a name="c3.3"></a>3.3. Etapas do CRISP-DM

&ensp; A metodologia CRISP-DM é composta por seis fases principais, que são interativas e não necessariamente sequenciais, permitindo um fluxo flexível e iterativo. Essas fases garantem uma abordagem completa e sistemática para projetos de mineração e análise de dados:

#### <a name="c3.3.1"></a>3.3.1. Entendimento do Negócio (Business Understanding)

&ensp;Esta é a fase inicial e talvez a mais crítica do CRISP-DM. O objetivo principal é compreender profundamente os objetivos e requisitos do projeto do ponto de vista do negócio. Isso envolve uma colaboração intensa com os stakeholders para definir claramente o problema a ser resolvido, os objetivos do projeto, os critérios de sucesso, os custos e os riscos associados.

#### <a name="c3.3.2"></a> 3.3.2. Entendimento dos Dados (Data Understanding)

&ensp; Nesta fase, a equipe se familiariza com os dados disponíveis. Isso envolve a coleta inicial dos dados, a exploração para identificar padrões, anomalias e relacionamentos, e a verificação da qualidade dos dados. O objetivo é obter uma compreensão aprofundada da estrutura, conteúdo e qualidade dos dados, garantindo que eles sejam relevantes, fidedignos e coerentes para os objetivos do negócio.

#### <a name="c3.3.3"></a>3.3.3. Preparação dos Dados (Data Preparation)

&ensp; A fase de preparação dos dados é frequentemente a mais demorada e trabalhosa. Ela envolve a transformação dos dados brutos em um formato adequado para a modelagem. As principais tarefas incluem:

- **Seleção**: Escolher os dados mais relevantes para o projeto.
- **Limpeza**: Lidar com dados ausentes, inconsistentes ou corrompidos.
- **Construção**: Criar novos atributos ou variáveis a partir dos dados existentes para melhorar a qualidade ou a relevância para a modelagem.
- **Integração**: Combinar dados de múltiplas fontes para criar um conjunto de dados unificado.
- **Formatação**: Transformar os dados para o formato exigido pelas ferramentas de modelagem.

&ensp; Dados de entrada de baixa qualidade resultarão em modelos de baixa qualidade, daí a importância crítica desta fase.

#### <a name="c3.3.4"></a>3.3.4. Modelagem (Modeling)

&ensp; Nesta fase, diversas técnicas de modelagem são aplicadas aos dados preparados para construir modelos que abordem os objetivos de negócio. Isso pode incluir algoritmos de Machine Learning, como árvores de decisão, redes neurais, regressão logística, entre outros. A equipe seleciona o algoritmo mais apropriado, define planos de teste para validação, constrói o modelo e o avalia. É comum que esta fase seja iterativa com a fase de Preparação dos Dados, pois a modelagem pode revelar a necessidade de novas transformações ou seleções de dados.

#### <a name="c3.3.5"></a>3.3.5. Avaliação (Evaluation)

&ensp; A fase de avaliação foca em analisar os resultados do modelo em relação aos objetivos de negócio definidos na primeira fase. Não se trata apenas de avaliar a precisão técnica do modelo, mas também de determinar se ele atende aos requisitos do negócio e se é capaz de gerar valor. É um momento crucial para revisar todo o processo, identificar novas necessidades ou problemas e decidir os próximos passos. Se os resultados não forem satisfatórios, pode ser necessário retornar a fases anteriores para refinar o modelo ou os dados.

#### <a name="c3.3.6"></a>3.3.6. Implantação (Deployment)

&ensp; Na fase final, os modelos validados são colocados em produção e integrados ao ambiente de negócio. Isso pode envolver a criação de relatórios, a implementação de sistemas automatizados ou a integração do modelo em aplicações existentes. Os objetivos desta fase incluem o planejamento da implantação, o monitoramento contínuo do desempenho do modelo e a manutenção para garantir sua eficácia a longo prazo.

<div align="center">
  <sub>Figura x - Processo do CRISP-DM</sub><br>
  <img src="/assets/CRISP-DM.png"><br>
  <sup>Fonte: Wikipédia</sup>
</div>

## <a name="c4"></a>4. Desenvolvimento e Resultados

### <a name="c4.1"></a>4.1. Compreensão do Problema

#### <a name="c4.1.1"></a>4.1.1. Contexto da indústria

&nbsp;A Chilli Beans está inserida em um setor híbrido que envolve moda, acessórios e, mais recentemente, o mercado óptico. Inicialmente, a marca consolidou-se como referência no segmento de óculos de sol e acessórios com forte apelo fashion, adotando o modelo de fast fashion. Nesse modelo, os produtos são renovados semanalmente com coleções exclusivas e temáticas, mantendo o dinamismo e a constante reinvenção como estratégia de diferenciação frente à concorrência.
&nbsp;A proposta da Chilli Beans é aliar estilo, acessibilidade e inovação em design, o que a posiciona como uma love brand entre o público jovem. Apesar de atuar em um mercado de moda vasto e competitivo, a empresa conquistou um espaço próprio ao investir fortemente em identidade visual, experiência de marca e formatos inovadores de venda, como quiosques em shoppings e ativações em eventos culturais e musicais.
&nbsp;Atualmente, a operação da Chilli Beans é estruturada em três formatos principais: quiosques, lojas tradicionais (as chamadas “lojas vermelhas”) e óticas voltadas para prescrição. Nos últimos anos, a marca passou a explorar de forma estratégica o setor oftalmológico com o lançamento das “Chilli Beans Óticas”. De acordo com o portal Investe SP, a empresa planeja abrir 400 novas óticas de rua até 2025, expandindo sua presença no varejo físico e diversificando o portfólio com foco em óculos de grau e lentes corretivas.
&nbsp;Esse movimento posiciona a marca em um contexto setorial que exige a compreensão tanto das tendências de moda e comportamento jovem quanto das oportunidades do setor óptico — impulsionado pelo envelhecimento populacional e pelo aumento da preocupação com a saúde visual. A capacidade da Chilli Beans de unir moda e funcionalidade configura-se como um diferencial competitivo em um ambiente de constante transformação, onde inovação, agilidade e posicionamento são fatores cruciais.

##### Recorte Setorial e Estrutura de Funcionamento

&nbsp;O setor óptico brasileiro é composto por diferentes elos da cadeia de valor: produção de lentes e armações, montagem e personalização, distribuição e varejo. As redes de óticas representam o elo final e mais visível ao consumidor, exercendo forte influência sobre a escolha das marcas e o posicionamento comercial dos produtos. Essa cadeia pode ser organizada da seguinte forma:

- Indústria de lentes e armações (ex: Luxottica, Essilor)  
- Montadoras e laboratórios ópticos  
- Distribuidores  
- Redes de varejo óptico  
- Pontos de venda multimarcas e franquias  
- Serviços oftalmológicos (prescrição médica)  

&nbsp;A Chilli Beans atua majoritariamente na etapa de varejo, com um modelo baseado em franquias. Suas coleções exclusivas seguem uma lógica semelhante à do fast fashion, aplicando-a ao setor óptico.

##### Fast Fashion e Aplicação ao Setor Óptico

&nbsp;O conceito de fast fashion refere-se a um modelo de negócios voltado para alta rotatividade de coleções, agilidade no lançamento de novos produtos e preços acessíveis. Marcas como Zara e H&M são referências nesse modelo, colocando novidades nas lojas semanalmente e incentivando o consumo contínuo.  
&nbsp;A Chilli Beans adapta esse modelo ao seu contexto ao lançar semanalmente novas coleções de óculos e relógios, muitas vezes com temáticas exclusivas, parcerias com artistas e tiragens limitadas. Essa abordagem híbrida busca conciliar exclusividade e diferenciação simbólica com acessibilidade, evitando o posicionamento premium de marcas como Ray-Ban, Lacoste ou Apple.

##### Principais Players e Disputa de Mercado

&nbsp;O mercado de óticas no Brasil é dominado por grandes redes. Em termos de receita, a Chilli Beans ocupa a terceira colocação, atrás de:

- **Luxottica / Óticas Carol – R$ 877 milhões (2018)**  
- **Óticas Diniz – R$ 847 milhões (2018)**  

&nbsp;Quanto ao número de unidades, a empresa também aparece em terceiro lugar, com 879 lojas, enquanto a Óticas Carol possui aproximadamente 1.200 e a Óticas Diniz, 1.000.  
&nbsp;O segmento de prescrição oftalmológica, três vezes maior que o de óculos de sol, representa uma importante oportunidade de crescimento. A abertura de novas unidades da “Chilli Beans Óticas” reforça a intenção da marca de competir diretamente com as líderes do setor tradicional.

##### Concorrência e Startups

&nbsp;Além das grandes redes, há uma crescente presença de startups ópticas e marcas direct-to-consumer, que oferecem lentes e armações com forte apelo digital, personalização e preços competitivos. Exemplos incluem **LIVO**, **Zerezes** e **Mooptica**. Essas empresas apostam em experiências de compra inovadoras, como prova virtual, e em práticas sustentáveis.  
&nbsp;Embora a Chilli Beans mantenha seu foco principal no varejo físico, também vem investindo em tecnologia e inovação como diferenciais competitivos.

##### Inovação e Transformação Tecnológica

&nbsp;A marca aposta em ferramentas tecnológicas que aprimoram a experiência de compra e reforçam seu posicionamento inovador, tais como:

- Espelhos inteligentes para experimentação virtual de óculos  
- Aplicativos com realidade aumentada  
- Integração com sistemas de prescrição óptica  
- Plataformas omnichannel para unificar os canais físicos e digitais  

&nbsp;Essas iniciativas fazem parte do reposicionamento estratégico da empresa, que visa ampliar sua atuação no setor de saúde visual sem abrir mão de seu apelo jovem e fashion.

##### Estratégia e Posicionamento

A estratégia da Chilli Beans combina elementos como:

- Narrativa emocional e storytelling em cada coleção  
- Tiragens limitadas para gerar escassez e exclusividade  
- Preços acessíveis, evitando a elitização  
- Modelo de franquias que garante ampla presença nacional  
- Alto ritmo de lançamentos, inspirado no fast fashion  

&nbsp;Contudo, essa abordagem pode gerar certa ambiguidade no posicionamento: ao buscar diferenciação simbólica sem adotar preços elevados, a marca ocupa um espaço intermediário — entre o popular e o premium — o que exige clareza na comunicação e segmentação do público-alvo.

##### Considerações Finais

&nbsp;O setor óptico brasileiro é robusto, regulado e competitivo, com margens apertadas e crescente transformação digital. A Chilli Beans destaca-se ao integrar moda e saúde visual em uma proposta única, que valoriza agilidade, inovação e experiência do consumidor. Seu modelo de negócio reflete as transformações do varejo contemporâneo, no qual é necessário combinar valor simbólico, eficiência operacional e conexão com o público.

---

#### Análise das Cinco Forças de Porter

<div align="center">
  <sub>Figura x - 5 Forças de Porter</sub><br>
  <img src="../assets/5_forcas_de_porter.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

##### 1. Ameaça de Novos Entrantes – Moderada

&nbsp;A entrada no setor de moda e acessórios é relativamente fácil do ponto de vista operacional (baixo custo inicial, fornecedores acessíveis e plataformas de venda digital). No entanto, a Chilli Beans já possui uma marca consolidada, forte identidade visual, fidelidade do público e uma estrutura logística ampla (com franquias, quiosques e pontos físicos estrategicamente posicionados), o que representa uma barreira competitiva significativa.

&nbsp;No ramo ótico, por outro lado, as exigências técnicas, regulamentações e necessidade de certificações criam uma barreira mais elevada, favorecendo players já estabelecidos.

**Resumo:** embora novos concorrentes possam surgir, competir com a força da marca e escala da Chilli Beans é desafiador.

---

##### 2. Poder de Barganha dos Fornecedores – Baixo a Moderado

&nbsp;A Chilli Beans mantém controle sobre grande parte da sua cadeia de produção, contando com produção terceirizada, principalmente na Ásia, e design interno exclusivo. Como a empresa opera em larga escala e possui forte poder de marca, ela consegue negociar preços competitivos com fornecedores.

&nbsp;Contudo, como parte da cadeia é internacional, questões logísticas (como flutuação cambial ou crises globais) podem aumentar custos ou comprometer prazos.

**Resumo:** a empresa possui certo poder de barganha, mas é vulnerável a fatores externos globais.

---

##### 3. Poder de Barganha dos Clientes – Moderado

&nbsp;No segmento de moda e acessórios, os clientes possuem diversas alternativas de marcas nacionais e internacionais. No entanto, a Chilli Beans investe fortemente em exclusividade de design, coleções temáticas e experiência de marca, o que reduz a sensibilidade ao preço e promove fidelização.

&nbsp;No setor ótico, o cliente tende a buscar mais confiança e qualidade técnica, o que pode aumentar o poder de decisão do consumidor. Ainda assim, a marca vem se posicionando com diferenciais de estilo e acessibilidade.

**Resumo:** os consumidores têm opções, mas a proposta única da Chilli Beans equilibra esse poder.

---

##### 4. Ameaça de Produtos Substitutos – Alta

&nbsp;Óculos, relógios e acessórios são itens que podem ser facilmente substituídos por outras marcas, genéricos ou produtos de segmentos correlatos (ex: eletrônicos vestíveis).

&nbsp;Além disso, o setor de moda sofre com altas taxas de rotatividade de tendências, o que demanda constante inovação. Em mercados online, a concorrência com grandes e-commerces e marcas digitais torna a substituição ainda mais fácil.

**Resumo:** o risco de substituição é alto, mas mitigado pela identidade e inovação contínua da marca.

---

##### 5. Rivalidade entre Concorrentes – Alta

&nbsp;O mercado de moda e acessórios é extremamente competitivo, com marcas locais e internacionais, incluindo grandes redes como Ray-Ban, Oakley, Lupo, além de marcas de fast fashion que vendem acessórios de baixo custo.

&nbsp;No segmento ótico, há empresas consolidadas como Óticas Carol, Chilli Beans Óticas, e óticas independentes, que disputam mercado com promoções agressivas e parcelamentos.

&nbsp;A Chilli Beans se destaca ao unir moda, exclusividade e presença multicanal (lojas, quiosques e óticas), o que a torna resistente. Porém, precisa manter inovação constante e boa gestão de marca para sustentar sua posição.

**Resumo:** a rivalidade é alta, mas a Chilli Beans possui diferenciais competitivos claros.

---

#### Conclusão Geral

&nbsp;A Chilli Beans atua em um setor de alta competitividade, onde o design exclusivo, posicionamento de marca e agilidade na oferta de produtos são suas maiores fortalezas. Sua estratégia de diferenciação no fast fashion de acessórios, somada à recente expansão no setor óptico, a coloca em um cenário de desafios, mas também de oportunidades.

##### Referências

- ABIÓPTICA. Case de sucesso Chilli Beans: tecnologia, proximidade e inovação. São Paulo: Associação Brasileira da Indústria Óptica, [2017]. Disponível em: <https://www.abioptica.com.br/case-de-sucesso-chilli-beans-tecnologia-proximidade-e-inovacao/>. Acesso em: 6 ago. 2025.

- ABIÓPTICA. Chilli Beans abre nova rede de óticas. São Paulo: Associação Brasileira da Indústria Óptica, [2018]. Disponível em: <https://www.abioptica.com.br/chilli-beans-abre-nova-rede-de-oticas/>. Acesso em: 6 ago. 2025.

- EXAME. Caito Maia e os negócios em expansão da Chilli Beans. São Paulo: Exame, 6 mar. 2024. Disponível em: <https://exame.com/negocios/caito-maia-chilli-beans-negocios-em-expansao-neex-2025/>. Acesso em: 6 ago. 2025.

- INVESTE SP. Plano de expansão da Chilli Beans prevê a abertura de 400 óticas de rua até 2025. São Paulo: Agência Paulista de Promoção de Investimentos e Competitividade, 4 fev. 2024. Disponível em: <https://www.investe.sp.gov.br/noticia/plano-de-expansao-da-chilli-beans-preve-a-abertura-de-400-oticas-de-rua-ate-2025/>. Acesso em: 6 ago. 2025.

#### <a name="c4.1.2"></a>4.1.2. Análise SWOT

&ensp;A análise SWOT é uma ferramenta estratégica essencial para diagnosticar Forças (Strengths) e Fraquezas (Weaknesses) internas, e Oportunidades (Opportunities) e Ameaças (Threats) externas de uma organização. No caso da Chilli Beans, essa análise revela que a marca é um dos principais players do mercado, embora existam pontos que exigem atenção constante por parte de seus gestores.

<div align="center">
  <sub>Figura x - Análise SWOT</sub><br>
  <img src="../assets/swot.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;Em resumo, a Chilli Beans se destaca pela inovação (Shopping Centers 2019), identidade ousada e compromisso com a qualidade e sustentabilidade (VEJA 2022). No entanto, apresenta fragilidades como a dependência de fabricantes internacionais (Isto É 2025) e os desafios para atrair novos clientes e impactar o público ideal para a empresa. Entre as oportunidades, destacam-se a expansão internacional (ABF 2021) e a presença em cidades menores por meio da linha Eco Chilli. Já entre as ameaças, estão o aumento da pirataria e um risco, ainda que baixo, de dano reputacional associado à exposição intensa do fundador Caito Maia nas redes sociais.

Fontes:
<sub>ASSOCIAÇÃO BRASILEIRA DE FRANCHISING. Chilli Beans e o mercado internacional: marca tem planos de expandir ainda mais no exterior em 2022. 2021. Disponível em: https://www.abf.com.br/franchisingbrasil/noticias/chilli-beans-e-o-mercado-internacional-marca-tem-planos-de-expandir-ainda-mais-no-exterior-em-2022/. Acesso em: 7 ago. 2025.</sub>
<sub>VEJA SP. Chilli Beans Eco Chilli. 2022. Disponível em: https://vejasp.abril.com.br/coluna/terraco-paulistano/chilli-beans-eco-chilli/. Acesso em: 7 ago. 2025.</sub>
<sub>MUNDO DO MARKETING. Risqué lança coleção em parceria com Chilli Beans. Disponível em: https://mundodomarketing.com.br/risque-lanca-colecao-em-parceria-com-chilli-beans. Acesso em: 7 ago. 2025.</sub>
<sub>REVISTA SHOPPING CENTERS. Chilli Beans: a marca que faz diferente o tempo todo. 2019. Disponível em: https://revistashoppingcenters.com.br/varejista/chilli-beans-a-marca-que-faz-diferente-o-tempo-todo/. Acesso em: 7 ago. 2025.</sub>
<sub>ISTO É DINHEIRO. Caito Maia Chilli Beans dinheiro entrevista. 2025. Disponível em: https://istoedinheiro.com.br/caito-maia-chilli-beans-dinheiro-entrevista. Acesso em: 7 ago. 2025.</sub>

#### <a name="c4.1.3"></a>4.1.3. Planejamento Geral da Solução

&ensp; O projeto propõe o desenvolvimento de modelos preditivos e analíticos para responder às três problemáticas apresentadas pela Chilli Beans: aumento de fluxo e conversão das Óticas de Rua, definição do público ideal e crescimento da categoria de óculos de grau. Para isso, serão utilizados dados fornecidos pela empresa, contemplando informações de vendas por loja, categorias de produtos, canais de venda, dados geográficos e demográficos, além de variáveis como gênero, idade, localização e histórico de compra dos clientes.

&ensp; A base de dados reúne identificadores, dados de ponto de venda, atributos de clientes e de produtos, são elementos necessários para relacionar registros, calcular métricas, segmentar públicos, comparar formatos de loja e analisar o mix de produtos para recomendações e campanhas.

&ensp; A solução será estruturada com base em técnicas de machine learning e métodos de clusterização, permitindo identificar padrões, segmentar públicos, detectar oportunidades de mercado e sugerir ações estratégicas. No caso das Óticas de Rua, o modelo apontará regiões e unidades com maior potencial de crescimento, considerando fatores socioeconômicos e concorrência. Para o público ideal, o foco será compreender perfis com maior probabilidade de conversão e fidelização, direcionando campanhas e comunicações personalizadas. Já para a categoria de grau, a análise buscará identificar barreiras de conversão, oportunidades de expansão e estratégias para aumentar a penetração dessa linha de produtos.

&ensp; Os resultados serão apresentados por meio de relatórios e visualizações interativas, como mapas geográficos, comparativos entre unidades e mapas de calor por segmento. Essas entregas servirão de apoio para decisões de marketing, expansão e gestão de produtos.

&ensp; Entre os principais benefícios esperados são aumento de visitas e conversão em lojas de rua, maior assertividade nas campanhas de marketing, engajamento de públicos estratégicos, crescimento do market share da categoria de grau e ampliação do ticket médio.

&ensp; O sucesso da solução será medido a partir de indicadores como crescimento percentual no fluxo e conversão das lojas, aumento na taxa de aquisição de clientes, evolução no desempenho da categoria de grau e relevância dos insights para o planejamento estratégico da empresa

#### <a name="c4.1.4"></a>4.1.4. Value Proposition Canvas

&ensp;O Canvas da Proposta de Valor é uma ferramenta visual e estratégica que garante o alinhamento perfeito entre o produto ou serviço oferecido e as reais necessidades do cliente, diminuindo os riscos de criar algo que ninguém quer. A estrutura se divide em dois lados: o Perfil do Cliente, onde se exploram as tarefas, dores e ganhos do público, e o Mapa de Valor, que detalha como os produtos e serviços atuam como "analgésicos" para as dores e "criadores de ganhos". Ao conectar esses dois lados, a ferramenta facilita a criação e comunicação de uma proposta de valor clara, relevante e atrativa. No presente projeto, aplicaremos esta ferramenta para estruturar a proposta de valor da nossa solução para a Chilli Beans.

<div align="center">

  <sub>Figura x - Value Proposition Canvas</sub>

  <img src="/assets/value_proposition_canvas.png">

  <sup>Fonte: Material produzido pelos autores (2025)</sup>

</div>

##### PROPOSTA DE VALOR

**Produtos & Serviços:**
&ensp;Esta seção detalha os produtos e serviços que compõem a proposta de valor entregue ao cliente.

- **1 -** Acesso prático aos produtos em diferentes canais de venda.

- **2 -** Ampliação do alcance e credibilidade da marca por meio de parcerias.

- **3 -** Experiência de compra unificada em plataformas digitais integradas.

- **4 -** Variedade que atende diferentes estilos e necessidades.

- **5 -** Coleções inspiradas em tendências que tornam a oferta mais atrativa.

**Criadores de Ganhos (Gain Creators)**<br>
&ensp;Esta seção detalha os elementos da solução que atuam como criadores de ganhos para o cliente, fortalecendo a proposta de valor.

- **1 -** Acesso rápido ao que há de mais atual na moda.

- **2 -** Possibilidade de expressar identidade única.

- **3 -** Sensação de exclusividade e singularidade.

- **4 -** Sentimento de pertencer a uma comunidade.

- **5 -** Reforço da imagem pessoal com autenticidade e confiança.

**Aliviadores de Dores (Pain Relievers)**<br>
&ensp;Esta seção apresenta os elementos da solução que atuam como aliviadores das dores identificadas no perfil do cliente.

- **1 -** Segurança ao adotar novas tendências.

- **2 -** Novidades constantes que quebram a monotonia.

- **3 -** Filtros e organização que economizam tempo na busca.

- **4 -** Processo de compra confiável e sem riscos.

- **5 -** Atualização frequente que evita sensação de obsolescência.

----

##### PERFIL DO CLIENTE (Público-Alvo Ideal)

**Tarefas do Cliente (Customer Jobs)**<br>
&ensp;Esta seção descreve as principais responsabilidades, objetivos e desafios enfrentados pelo cliente, que orientam o desenvolvimento da solução.

- **1 -** Expressar essência e personalidade.

- **2 -** Acompanhar tendências para manter-se atualizado.

- **3 -** Buscar referências para criar ou reinventar o visual.

- **4 -** Encontrar produtos com boa relação custo-benefício.

- **5 -** Construir imagem pessoal coerente com seus objetivos.

**Ganhos Desejados (Gains)**<br>
&ensp;Esta seção apresenta os ganhos esperados pelo cliente com a adoção da solução.

- **1 -** Sentir-se único por meio da exclusividade.

- **2 -** Fazer parte de uma comunidade com interesses similares.

- **3 -** Facilidade para acompanhar tendências.

- **4 -** Confiança para experimentar novos estilos.

- **5 -** Experiência de compra positiva e satisfatória.

**Dores (Pains)**<br>
&ensp;Esta seção descreve os principais desafios e obstáculos enfrentados pelo cliente.

- **1 -**  Medo de ser visto como “básico” e sem originalidade.

- **2 -** Dificuldade para encontrar produtos autênticos em meio ao mercado saturado.

- **3 -** Limitação orçamentária que exige conciliar estilo e preço.

- **4 -** Insegurança para inovar e medo de rejeição social.

- **5 -** Falta de tempo para pesquisar e escolher produtos.

&ensp;Dessa forma, é possível perceber, por meio do modelo apresentado, que a solução proposta não apenas busca aliviar as dores identificadas no perfil do cliente, mas também visa potencializar ganhos relevantes em sua experiência de consumo.

#### <a name="c4.1.5"></a>4.1.5. Matriz de Riscos

&ensp;A matriz de riscos é uma ferramenta fundamental de gestão de projetos que permite a identificação, avaliação e priorização sistemática dos riscos que podem afetar o sucesso do projeto.

&ensp;A matriz facilita a tomada de decisões estratégicas ao categorizar os riscos em diferentes níveis de criticidade, permitindo que as equipes de projeto concentrem seus esforços nas ameaças mais significativas e nas oportunidades de maior valor. A estrutura adotada segrega os riscos em duas categorias principais: ameaças (eventos que podem prejudicar os objetivos do projeto) e oportunidades (eventos que podem agregar valor além do esperado), proporcionando uma visão equilibrada dos cenários possíveis durante a execução do projeto.

<div align="center">  
  <sub>Figura x - Matriz de risco</sub><br>
  <img src="../assets/matriz-risco.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

##### Lista de Riscos Identificados

###### Ameaças

**A1 - Qualidade dos dados insuficiente ou inconsistente:** Dados históricos de clientes podem apresentar lacunas, inconsistências ou baixa qualidade que comprometam a precisão dos modelos preditivos e análises de clusterização.

**A2 - Possível descumprimento da LGPD e normas de proteção de dados:** O uso de dados pessoais para segmentação e personalização pode violar normas de proteção de dados, gerando riscos legais e regulatórios.

**A3 - Modelo Preditivo Ineficaz:** Os algoritmos podem desenvolver vieses que discriminem determinados grupos demográficos, comprometendo a equidade e eficácia das estratégias de marketing.

**A4 - Resistência organizacional às mudanças estratégicas:** Equipes de marketing e vendas podem resistir à adoção de novas abordagens baseadas em dados, preferindo métodos tradicionais.

**A5 - Alteração no escopo do projeto:**  Alterações no escopo podem desviar o projeto dos objetivos iniciais.

###### Oportunidades

**O1 - Descoberta de novos nichos de mercado rentáveis:** A análise de dados pode revelar segmentos de clientes não explorados com alto potencial de conversão e fidelização.

**O2 - Melhoria significativa no retorno das campanhas de marketing:** A personalização baseada em clusters pode aumentar drasticamente a efetividade das campanhas publicitárias e de comunicação.

**O3 - Descoberta de padrões comportamentais inesperados:** Os modelos podem revelar insights comportamentais surpreendentes que abram novas possibilidades de segmentação e estratégias não previstas inicialmente.

**O4 - Expansão para novos segmentos demográficos:** Insights sobre comportamento geracional podem facilitar a penetração em faixas etárias ou regiões com baixa participação atual.

**O5 -  Integração bem-sucedida com sistemas existentes:** Os modelos preditivos podem ser integrados de forma mais eficiente que o esperado com os sistemas atuais de CRM e marketing, facilitando a implementação prática das recomendações.

##### Justificativas das Classificações

###### A1 - Qualidade dos dados

&ensp;Em projetos de análise de dados de varejo, problemas de qualidade são extremamente comuns, incluindo registros duplicados, campos em branco, inconsistências de formato e dados desatualizados. A natureza multi-marca da empresa, envolvendo Ótica Chilli Beans, Chilli Beans e Eco Chilli, aumenta significativamente a probabilidade de inconsistências entre diferentes sistemas de dados. Experiências anteriores em projetos similares demonstram que questões de qualidade de dados ocorrem em praticamente todos os casos, variando apenas em grau de severidade.

&ensp;Embora problemas de qualidade de dados não impeçam completamente a execução do projeto, eles podem reduzir significativamente a precisão dos modelos preditivos e das análises de clusterização. Isso resulta em segmentações menos precisas e estratégias de marketing com eficácia reduzida, impactando moderadamente os resultados esperados sem inviabilizar completamente os objetivos do projeto.

###### A2 - Não conformidade LGPD

&ensp;O uso de dados pessoais para segmentação e personalização é dependente ao escopo do projeto. Com a Lei Geral de Proteção de Dados em pleno vigor e fiscalização crescente por parte das autoridades competentes, existe possibilidade de questões de conformidade surgirem durante o desenvolvimento do projeto. Análises comportamentais detalhadas e segmentação de clientes envolvem necessariamente o tratamento de dados pessoais, requerendo atenção adequada aos aspectos regulatórios.

&ensp;Questões de conformidade com a LGPD podem gerar multas e exigir ajustes nos processos e metodologias do projeto, embora raramente impeçam sua execução completa. As penalidades são geralmente proporcionais à gravidade das infrações e podem ser controladas através de adequações processuais e implementação de medidas corretivas apropriadas.

###### A3 - Modelo Preditivo Ineficaz

&ensp;Algoritmos de machine learning podem apresentar desempenho abaixo do esperado devido a diversos fatores, incluindo dados de treinamento inadequados, seleção inapropriada de variáveis, hiperparâmetros mal ajustados ou vieses nos dados históricos. A probabilidade é considerada moderada porque, embora seja um risco comum em projetos de ciência de dados, pode ser mitigado através de metodologias robustas de desenvolvimento e validação de modelos.

&ensp;Um modelo preditivo ineficaz pode comprometer significativamente os objetivos principais do projeto, resultando em segmentações imprecisas de clientes e estratégias de marketing baseadas em predições incorretas. Isso afetaria diretamente a eficácia das campanhas personalizadas e poderia levar a decisões de negócio inadequadas, impactando substancialmente o retorno sobre investimento esperado do projeto.

###### A4 - Resistência organizacional

&ensp;A Chilli Beans demonstra ser uma marca reconhecidamente inovadora e com orientação para soluções baseadas em dados, conforme evidenciado pela própria iniciativa deste projeto. A probabilidade de resistência organizacional significativa é considerada baixa, especialmente considerando que a implementação pode ser acompanhada de treinamento adequado das equipes e demonstração gradual dos resultados positivos iniciais.
&ensp;Caso a resistência organizacional se materialize, pode causar atrasos significativos na implementação das recomendações geradas pelo projeto e na adoção das novas metodologias de marketing. Isso comprometeria substancialmente o retorno sobre investimento esperado e a efetividade prática dos resultados, impactando diretamente o sucesso do projeto no ambiente operacional real da empresa.

###### A5 - Alteração no escopo do projeto

&ensp;Mudanças no escopo durante o desenvolvimento do projeto podem ocorrer devido a novos requisitos de negócio, alterações nas prioridades organizacionais ou descobertas que levem a redirecionamentos estratégicos. A probabilidade é considerada baixa devido ao nível de planejamento e definição de objetivos específicos já estabelecidos para o projeto, embora mudanças organizacionais sempre sejam possíveis.

&ensp;Alterações no escopo podem gerar retrabalho, necessidade de revisão de metodologias e potencial aumento de prazos e custos do projeto. Embora possa afetar a eficiência da execução e demandar readequação de recursos, raramente compromete os objetivos fundamentais do projeto, especialmente se as mudanças forem bem gerenciadas e incorporadas de forma estruturada ao planejamento.

###### O1 - Novos nichos de mercado

&ensp;Projetos de análise de dados e clusterização frequentemente revelam segmentos de clientes não identificados previamente pelas metodologias tradicionais de análise de mercado. Com a Chilli Beans operando três marcas distintas e possuindo uma ampla base de dados de clientes, há alta probabilidade de descobrir nichos de mercado inexplorados que apresentem características e comportamentos específicos não adequadamente atendidos pelas estratégias atuais.

&ensp;A descoberta de novos nichos de mercado rentáveis pode representar oportunidades significativas de aumento de receita e expansão estratégica da empresa. Estes segmentos podem direcionar o desenvolvimento de novas linhas de produtos, estratégias de comunicação específicas e abordagens de marketing diferenciadas, proporcionando crescimento sustentável e fortalecimento da posição competitiva no mercado de ótica e acessórios.

###### O2 - Retorno melhorado das campanhas

&ensp;A personalização de campanhas de marketing baseada em análise de dados e segmentação de clientes possui histórico comprovado de melhoria na efetividade e retorno sobre investimento. Considerando que este é um dos objetivos principais do projeto, há alta probabilidade de algum nível de melhoria mensurável no desempenho das campanhas publicitárias e de comunicação da empresa, embora o grau específico de melhoria possa variar.

&ensp;A melhoria no retorno sobre investimento das campanhas de marketing tem impacto direto na lucratividade da empresa. Este benefício pode justificar rapidamente o investimento realizado no projeto e proporcionar recursos adicionais para reinvestimento em novas iniciativas de marketing e expansão das estratégias de personalização.

###### O3 - Padrões comportamentais inesperados

&ensp;A descoberta de insights comportamentais genuinamente surpreendentes e reveladores é menos previsível que outros tipos de resultados de análise de dados. A probabilidade depende significativamente da riqueza e diversidade dos dados disponíveis, bem como da sofisticação das técnicas analíticas aplicadas. Embora seja possível, não há garantia de que os padrões descobertos sejam suficientemente inovadores para transformar as estratégias existentes.

&ensp;Padrões comportamentais inesperados podem abrir novas possibilidades de segmentação e estratégias de marketing não contempladas anteriormente. O valor real desta oportunidade depende diretamente de quão aplicáveis e implementáveis sejam os insights descobertos na prática operacional da empresa, podendo gerar vantagens competitivas importantes.

###### O4 - Novos segmentos demográficos

&ensp;Embora a identificação de oportunidades em novos segmentos demográficos seja provável através da análise de dados, o sucesso efetivo na expansão para estes segmentos é mais desafiador. A materialização desta oportunidade requer não apenas a identificação dos segmentos, mas também investimentos adicionais em adaptação de produtos, canais de distribuição e estratégias de comunicação que vão além do escopo direto do projeto de análise de dados.

&ensp;A penetração bem-sucedida em novos segmentos demográficos, particularmente em diferentes faixas etárias ou regiões geográficas, pode representar crescimento da base de clientes e receita da empresa. Esta expansão tem potencial de ampliar o alcance de mercado das marcas Chilli Beans, proporcionando oportunidades de crescimento de longo prazo e diversificação da base de clientes.

###### O5 - Integração com sistemas existentes

&ensp;A integração de modelos preditivos com sistemas existentes de CRM e plataformas de marketing é tecnicamente factível utilizando ferramentas e metodologias modernas disponíveis no mercado. O sucesso desta integração depende principalmente de planejamento técnico adequado e coordenação apropriada entre as equipes de tecnologia e marketing, fatores que estão amplamente sob controle da organização.

&ensp;Uma integração bem-sucedida facilita significativamente a implementação prática dos resultados do projeto, reduz custos operacionais de manutenção e atualização dos modelos, e torna o projeto mais sustentável operacionalmente no longo prazo. Embora não transforme diretamente o modelo de negócio da empresa, proporciona eficiência operacional importante para o aproveitamento contínuo dos benefícios gerados pelo projeto.

#### <a name="c4.1.6"></a>4.1.6. Personas

&ensp;Personas são arquétipos — modelos que representam padrões de comportamento e expectativas — fictícios, mas fundamentados em dados, que descrevem diferentes perfis de usuários de um produto, serviço ou sistema. O propósito fundamental das personas é guiar as decisões de estratégia, experiência do usuário e desenvolvimento, garantindo que a solução final esteja centrada nas pessoas que irão utilizá-la e seja verdadeiramente eficaz para resolver suas dores.

&ensp;No presente projeto, as personas foram elaboradas como protopersonas, ou seja, hipóteses coletivas formuladas a partir do material disponibilizado pela Chilli Beans e de análises iniciais do grupo.

&ensp;As quatro personas desenvolvidas representam papéis complementares. Vanessa, coordenadora de expansão, reflete a visão estratégica da empresa na escolha de regiões e unidades para crescimento. Caio, analista de CRM e Inteligência de Clientes, conecta dados e marketing, garantindo segmentação e explicabilidade. Mariana, arquiteta, simboliza clientes que já consomem armações, mas hesitam em confiar lentes de grau à marca. Gabriela, jovem universitária, traduz o perfil de consumidoras digitais que valorizam estilo, preço acessível e disponibilidade de produtos.

<div align="center">
<sub>Figura X - Descrição da persona Vanessa</sub>

![Persona vanessa](../assets/secao-4-1-6/persona_vanessa.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;A persona **Vanessa** é responsável por definir as regiões prioritárias para a abertura de lojas, principalmente das óticas de rua, além de direcionar campanhas estratégicas. No entanto, ela enfrenta dificuldades devido à necessidade de realizar processos manuais e demorados de coleta e análise de dados. Sua principal dor reside no excesso de esforço exigido e na ausência de ferramentas integradas que agilizem seu trabalho, dificultando a garantia do melhor desempenho das unidades, especialmente as de rua.

<div align="center">
<sub>Figura X - Descrição da persona Caio</sub>

![Persona Caio](../assets/secao-4-1-6/persona_caio.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;A persona **Caio** representa o perfil de um analista de CRM e Inteligência de Clientes que busca segmentar a base com precisão para criar campanhas personalizadas mais eficazes. Formado em Estatística, enfrenta dificuldades para prever o comportamento dos clientes e sente falta de ferramentas preditivas que aproveitem todo o potencial dos dados. Sua maior dor é lidar com a incerteza e a necessidade de explicar claramente os resultados apresentados. Ele precisa de ferramentas que ofereçam explicabilidade dos dados e flexibilidade para ajustar o ritmo das decisões, acelerando ou aprofundando as análises conforme a demanda. Seu cenário inclui reuniões com executivos e stakeholders, nas quais é essencial comunicar recomendações de forma objetiva e fundamentada.

<div align="center">
<sub>Figura X - Descrição da persona Mariana</sub>

![Persona Mariana](../assets/secao-4-1-6/persona_mariana.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;A persona **Mariana** representa a consumidora que valoriza estilo e design, já sendo cliente da Chilli Beans na categoria de armações, mas que não enxerga a marca como especialista em lentes de grau. Embora goste do design das armações, opta por outras óticas no momento da compra das lentes. Suas dores estão ligadas principalmente à insegurança durante o processo de escolha: ela sente falta de informações claras sobre qualidade, proteção e compatibilidade das lentes, além de perceber a compra da armação e da lente como processos separados, o que gera esforço adicional e diminui sua confiança. Também existe uma desconfiança sobre a expertise da Chilli Beans nessa categoria. Para Mariana, o que destrava a confiança é a presença de orientações simples e confiáveis que a guiem no momento da compra, acompanhadas por selos de autoridade e mensagens que reforcem a especialização da marca. Transparência nos atributos do produto, como tecnologia, proteção e garantia, também é fundamental. A solução dialoga com suas necessidades ao integrar a jornada de compra de armação e lente, fornecer informações visuais claras e reforçar a credibilidade da Chilli Beans como referência em lentes de grau.

<div align="center">
<sub>Figura X - Descrição da persona Gabriela</sub>

![Persona Gabriela](../assets/secao-4-1-6/persona_gabriela.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;A persona **Gabriela** simboliza a jovem em busca de pertencimento, uma universitária de 21 anos que utiliza os acessórios de moda para afirmar identidade e expressar estilo. Ela é sensível a preço e disponibilidade, mas busca produtos que unam acessibilidade com autenticidade. Suas principais dores estão relacionadas à dificuldade em encontrar variedade de modelos que reforcem sua individualidade, ao sentimento de não pertencimento quando a comunicação não reflete diversidade e à frustração com a indisponibilidade de modelos ou tamanhos nas lojas. Gabriela precisa de produtos alinhados às tendências que acompanha nas redes sociais, mas que também estejam disponíveis em pontos de venda e dentro de uma faixa de preço acessível. Além disso, valoriza campanhas que comuniquem diversidade, autenticidade e representatividade, validando seu perfil e fortalecendo o vínculo com a marca. A solução conecta-se a essas necessidades ao possibilitar segmentações que destacam linhas jovens e acessíveis, além de campanhas que reforçam diversidade como atributo central e estratégias para garantir disponibilidade de produtos, evitando frustração e construindo fidelidade.

&ensp;Assim, as quatro personas — Vanessa, Caio, Mariana e Gabriela — foram desenvolvidas para representar de forma precisa diferentes perfis e necessidades estratégicas da marca. Cada uma traz perspectivas complementares sobre desafios e oportunidades, assegurando que as soluções propostas sejam orientadas por necessidades reais, ampliem a eficácia das ações e mantenham o foco no cliente em todas as etapas do projeto.
&ensp;Entretanto, é importante reconhecer que a aplicação de tecnologias de análise preditiva e segmentação em estratégias de marketing não é neutra. Essas ferramentas influenciam diretamente as escolhas de consumo, podem estimular gastos não planejados e moldar percepções de valor dos clientes. Nesse sentido, o grupo entende que sua utilização deve estar acompanhada de salvaguardas éticas claras, priorizando transparência na comunicação, respeito à privacidade dos dados e responsabilidade no direcionamento das campanhas. Ao adotar esse posicionamento crítico, busca-se não apenas potencializar resultados de negócio, mas também assegurar que a solução proposta contribua para relações de consumo mais conscientes, equilibradas e sustentáveis.

#### <a name="c4.1.7"></a>4.1.7. Jornadas do Usuário

&ensp;Um mapa da jornada do usuário é uma representação visual que descreve as etapas, experiências, emoções e pontos de contato que uma pessoa percorre ao interagir com um produto, serviço ou marca. Ele ajuda a compreender a perspectiva do usuário, identificando suas necessidades, expectativas e possíveis dores ao longo do processo. Essa ferramenta é essencial para alinhar equipes, encontrar oportunidades de melhoria e criar soluções mais eficazes e centradas no cliente, garantindo uma experiência mais fluida e satisfatória.

&ensp;No contexto deste projeto, foram elaborados dois mapas de jornada do usuário a partir da persona Vanessa, coordenadora de expansão da Chilli Beans, cuja principal responsabilidade é identificar potenciais locais para a abertura de novas lojas da marca. Esses mapas permitem comparar sua experiência em dois cenários distintos: no primeiro, Vanessa realiza sua tarefa sem o apoio de um modelo preditivo, enfrentando maior desgaste, demora no processo e incertezas na tomada de decisão; no segundo, ela conta com o auxílio da solução proposta, que otimiza sua rotina, tornando-a mais ágil, confiável e completa. A seguir, apresentamos o primeiro mapa da jornada do usuário, no qual Vanessa desempenha suas atividades sem o suporte do modelo preditivo.

<div align="center">
  <sub>Figura x - Jornada do Usuário antes do modelo preditivo</sub><br>
  <img src="../assets/user-journey-before.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;Antes de ter acesso a um modelo preditivo, a rotina de Vanessa era marcada por um processo cansativo e repetitivo. Para cumprir sua tarefa de identificar potenciais locais de abertura de novas lojas, ela precisava buscar dados sociodemográficos em diferentes fontes, como o IBGE, o Ministério do Trabalho e pesquisas de mercado específicas. Essa etapa já demandava muito tempo, pois exigia também a verificação de planilhas antigas no Excel com informações necessárias para dar continuidade ao trabalho. Após a coleta de um conjunto extenso de dados, Vanessa ainda tinha que organizá-los manualmente em uma única planilha, estruturando indicadores que possibilitassem comparações entre diferentes regiões.

&ensp;O trabalho se tornava ainda mais complexo quando chegava o momento de filtrar e identificar manualmente os bairros alinhados ao perfil da marca, além de realizar comparações detalhadas entre eles. Todo esse processo exigia muito esforço, alto nível de atenção para evitar erros e gerava uma sensação constante de desgaste, já que cada nova análise significava recomeçar um ciclo de tarefas manuais e demoradas.

<div align="center">
  <sub>Figura x - Jornada do Usuário depois do modelo preditivo</sub><br>
  <img src="../assets/user-journey-after.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;Com a implementação do modelo preditivo, a experiência de Vanessa mudou de forma significativa. Em vez de lidar com buscas manuais e cruzamento de dados dispersos, ela passou a receber relatórios mensais já estruturados, contendo gráficos claros e análises objetivas. Isso reduziu consideravelmente o esforço operacional, já que grande parte do trabalho inicial estava pronta ao seu alcance. Dessa forma, Vanessa conseguiu direcionar seu tempo para avaliar os resultados, selecionar os pontos mais relevantes e preparar materiais de apresentação com maior agilidade. O processo se tornou mais confiável e menos desgastante, permitindo que suas decisões fossem tomadas com base em informações precisas e seguras. O resultado foi uma rotina mais produtiva, na qual a energia antes dedicada a tarefas repetitivas pôde ser investida em análises estratégicas e no fortalecimento de suas recomendações à diretoria.

&ensp;Dessa forma, ao comparar o cenário anterior com o posterior à adoção do modelo preditivo, percebe-se uma mudança significativa na rotina de Vanessa. O processo que antes exigia longas horas de pesquisa, consolidação e análise manual de dados passou a ser automatizado, entregando relatórios bem estruturados, gráficos intuitivos e insights objetivos. O modelo preditivo opera a partir da integração de múltiplas fontes de dados sociodemográficos e de mercado, aplicando algoritmos estatísticos e técnicas de aprendizado de máquina para identificar padrões de consumo e estimar o potencial de cada região. Com isso, Vanessa pôde dedicar mais tempo à avaliação dos resultados e à tomada de decisões estratégicas, alcançando maior assertividade na escolha das regiões e reduzindo de forma expressiva o esforço operacional. O modelo não apenas otimizou sua produtividade, mas também trouxe mais confiabilidade, agilidade e segurança para todo o processo decisório.

#### <a name="c4.1.8"></a>4.1.8. Política de Privacidade

&ensp;A Política de Privacidade constitui um documento fundamental que estabelece as diretrizes para o tratamento de dados pessoais por uma entidade. Sua função primordial é oferecer total transparência ao titular dos dados, detalhando quais informações são coletadas, as finalidades que justificam sua utilização, as medidas de segurança adotadas para sua proteção e armazenamento, e as condições sob as quais pode ocorrer o compartilhamento com terceiros. Dessa forma, a política formaliza um compromisso com a privacidade e a segurança, sendo um instrumento essencial para garantir a conformidade com a legislação vigente, notadamente a Lei nº 13.709/2018 (Lei Geral de Proteção de Dados - LGPD).

#### Política de Privacidade - Hot Data

**Informações Gerais**
Esta política de privacidade informa como o projeto Hot Data, desenvolvido pelo grupo Red Hot Chili Peppers, trata os dados recebidos da empresa parceira Chilli Beans. O tratamento de dados está em conformidade com a Lei nº 13.709/2018 – Lei Geral de Proteção de Dados (LGPD).

**Dados Coletados**
O projeto utilizará uma base de dados fornecida pela empresa parceira Chilli Beans no que tange informações de vendas por loja, tipo de produto e informações não sensíveis de consumidores. Os dados utilizados para a construção dos modelos analíticos incluem:

- **Dados de Transação:** Identificadores de vendedor, cliente e produto, data da venda, número do pedido, quantidade, histórico de compra e valor dos itens.

- **Dados de Produto:** Categoria do produto, referência e nome do produto, coleção (Griffe) e tipo de marca (Canal).

- **Dados Demográficos e Geográficos dos Clientes:** Gênero declarado, idade (calculada a partir da data de nascimento), cidade e estado do cliente.

- **Dados de Loja (Ponto de Venda):** Identificação da loja, nome da loja e tipo de ponto de venda (Rua, quiosque, shopping, etc.).

Em conformidade com as restrições do projeto, nenhum dado sensível como CPF, nome completo, endereços completos, dados clínicos ou CNPJ será utilizado.

**Finalidade do Tratamento**
Os dados serão tratados com as seguintes finalidades, estritamente acadêmicas e vinculadas aos objetivos do projeto:

- **Para as Óticas de Rua:** Desenvolver um modelo preditivo para apontar regiões e unidades com maior potencial de crescimento, visando o aumento de fluxo e conversão.

- **Para o Público Ideal:** Aplicar métodos de clusterização para segmentar e compreender os perfis de clientes com maior probabilidade de conversão e fidelização, permitindo o direcionamento de campanhas e comunicações personalizadas.

- **Para a Categoria de Grau:** Construir um modelo analítico para identificar barreiras de conversão, oportunidades de expansão e estratégias para aumentar a penetração da linha de produtos de grau.

Os dados recebidos _não serão utilizados para treinar ou alimentar outras ferramentas de Inteligência Artificial externas_ ao escopo do projeto, garantindo a proteção das informações da marca.

**Armazenamento, Retenção, Compartilhamento e Segurança**

- **Armazenamento e Retenção:** Os dados serão armazenados em ambiente de nuvem seguro, providenciado pela instituição de ensino Inteli, com acesso restrito aos membros do grupo de desenvolvimento. Os dados serão mantidos apenas durante o ciclo de vida do projeto e serão permanentemente excluídos após a sua conclusão, expondo apenas o código base para os modelos preditivos no site do Inteli (open source).

- **Compartilhamento de Dados:** Os dados brutos não serão compartilhados com terceiros. Os resultados do projeto (análises, relatórios e dashboards com dados agregados e anonimizados) serão compartilhados apenas com a empresa parceira Chilli Beans e com o corpo docente do Inteli para fins avaliativos.

- **Segurança dos Dados:** Medidas de segurança como controle de acesso rigoroso, uso de senhas fortes e armazenamento em plataformas seguras são adotadas para proteger os dados contra acesso não autorizado, alteração, divulgação ou destruição.

**Direitos dos Titulares e Contato do DPO**

- **Direitos dos Titulares:** Como o projeto utiliza uma base de dados fornecida pelo parceiro, as solicitações de direitos dos titulares, como acesso, correção e exclusão de dados, devem ser direcionadas diretamente aos canais de atendimento da Chilli Beans.

- **Encarregado de Dados (DPO) do Projeto:** Para qualquer dúvida sobre como este projeto trata os dados, o contato é:

  - **Nome:** Suporte Chilli Beans
  - **Contato:** +55 (11) 3173-2050
  - **E-mail:** <<EMAIL>>

### <a name="c4.2"></a>4.2. Compreensão dos Dados

&ensp;A compreensão dos dados é um processo crucial para garantir que as informações sejam utilizadas de forma eficaz e precisa. Nesta seção, abordaremos a exploração inicial dos dados, identificação de hipóteses e preparação dos dados para modelagem.

#### <a name="c4.2.1"></a>4.2.1. Exploração de dados

&ensp;A exploração inicial dos dados envolveu a análise de variáveis relevantes para compreender o comportamento de vendas da empresa. Através de gráficos e tabelas, foram identificadas tendências, padrões e insights que direcionaram as hipóeses a serem testadas.

#### Estatística Descritiva das Colunas

&ensp;A estatística descritiva foi aplicada a todas as colunas numéricas do dataset, visando compreender a distribuição e as principais estatísticas de cada variável. Isso incluiu a análise de medidas de tendência central (média, mediana, moda), dispersão (desvio padrão, mínimo, máximo) e contagem de valores.

##### Seleção das Colunas Úteis

&nbsp; A definição das colunas analisadas não se baseou apenas na disponibilidade do dataset, mas sim em **critérios de relevância estatística e impacto para a tomada de decisão**.  
&nbsp;Os principais critérios adotados foram:

- **Representatividade de métricas financeiras e operacionais**  
  - &nbsp; Colunas como `Valor_total`, `Preco_Varejo` e `DESCONTO_CALCULADO` foram incluídas por refletirem diretamente a receita gerada, as margens de lucro e as estratégias de preço aplicadas.  
  - &nbsp; Variáveis como `Quantidade` e `Data_Venda` permitem avaliar o volume de vendas e a sazonalidade, possibilitando análises de desempenho por produto, loja ou canal.  

- **Segmentação de clientes e comportamento de consumo**  
  - &nbsp; Informações sobre os clientes (`Dim_Cliente.Cidade_cliente`, `Dim_Cliente.Uf_Cliente`, `Dim_Cliente.Genero`, `Dim_Cliente.Data_Nascimento`) foram consideradas relevantes para identificar padrões geográficos e demográficos de consumo.  
  - &nbsp; A derivação da **faixa etária** a partir da data de nascimento possibilita cruzamentos com tipos de produto, permitindo compreender preferências por idade.  

- **Análise de canais e pontos de venda**  
  - &nbsp; Colunas referentes às lojas (`Tipo_PDV`, `CANAL_VENDA`) permitem avaliar a performance por canal de venda (lojas físicas, quiosques e e-commerce), comparando resultados entre diferentes modelos de operação.  

- **Classificação de produtos**  
  - &nbsp; A coluna `Dim_Produtos.Grupo_Produto` permite categorizar os itens vendidos, possibilitando análises de performance por categoria e identificação de produtos estratégicos para a empresa.  

&nbsp; Em resumo, a seleção destas colunas buscou garantir que todas as dimensões críticas — **vendas, clientes, produtos e canais** — fossem contempladas, permitindo análises completas que subsidiam decisões estratégicas de precificação, marketing e expansão comercial.  

---

##### Agregação dos Dados

&nbsp;Após a seleção, realizamos a **clusterização das variáveis em numéricas e categóricas**:

- **Colunas Numéricas:**
 &nbsp;Incluem variáveis como `Preco_Custo`, que representa o custo de produção de cada produto, e `Desconto_Calculado`, que indica o percentual de desconto aplicado. Outras variáveis analisadas, como `Quantidade` e `Valor_Total`, foram fundamentais para a avaliação de volume e receita.  

- **Colunas Categóricas:**
&nbsp;Englobam identificadores de funcionário, cliente e loja, além de atributos como `Dim_Produtos.Grupo_Produto`, `Dim_Produtos.Grupo_Produto` e `REGIAO_CHILLI`. Estas variáveis permitiram segmentar os dados em diferentes dimensões de análise, como perfil de público e desempenho por ponto de venda.  

&nbsp; Essa separação possibilitou aplicar **estatísticas descritivas adequadas a cada tipo de dado**:  

- &nbsp; Para variáveis **numéricas**, foram calculadas média, mediana, moda, desvio padrão, valores mínimos e máximos.  
- &nbsp; Para variáveis **categóricas**, foram analisadas as frequências absolutas e relativas das categorias.  

---

##### Estatística descritiva das colunas

&nbsp;Escolheram-se quatro colunas numéricas do conjunto de dados para a realização da análise estatística: `Preco_Custo`, `DESCONTO_CALCULADO`, `Quantidade` e `Valor_Total`. O objetivo é compreender o comportamento dessas variáveis por meio de medidas descritivas, tais como **média, mediana, moda, desvio padrão, valores mínimos e máximos**, além da soma total e da contagem de registros. Essa análise fornece uma visão geral da distribuição e variabilidade dos dados, servindo como base para interpretações mais aprofundadas e para a identificação de possíveis padrões ou anomalias. [Clique aqui](../notebooks/estatistica_descritiva.ipynb) para acessar o notebook com o código completo.

<div align="center">
  <sub>Tabela X - Tabela de estatística descritiva</sub>
</div>

| Estatística        | Preco_Custo       | DESCONTO_CALCULADO | Quantidade      | Valor_Total       |
|--------------------|------------------:|-------------------:|----------------:|------------------:|
| Média              | 76.37             | 8.05               | 1.09            | 305.63            |
| Mediana            | 73.26             | 0.00               | 1.00            | 322.14            |
| Moda               | 69.21             | 0.00               | 1.00            | 399.98            |
| Desvio Padrão      | 75.19             | 56.36              | 0.33            | 316.62            |
| Soma Total         | 1,402,644.88      | 147,769.52         | 20,024.00       | 5,613,492.68      |
| Mínimo             | 1.80              | -81.74             | 1.00            | 0.01              |
| Máximo             | 1,379.16          | 3,018.01           | 12.00           | 11,057.97         |
| Contagem Valores   | 18,367            | 18,367             | 18,367          | 18,367            |

<div align="center">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

---

##### Gráficos e Visualizações

&ensp;Através de gráficos e visualizações, buscamos compreender as relações e padrões nos dados, identificando insights relevantes para a tomada de decisão.

##### 1. Ticket médio por canal de vendas e por região nas óticas

&nbsp;O ticket médio representa o valor médio de cada compra, sendo uma métrica fundamental para avaliar o poder de geração de receita de cada canal de venda. A análise por região permite identificar padrões geográficos de consumo e desempenho dos canais. Estes insights são focados na Problemática 1: Aumento de fluxo e conversão das Óticas de Rua.
&ensp;Através da análise do ticket médio por canal de vendas e por região, é possível identificar onde há maior volume de vendas e onde o ticket médio é mais elevado, permitindo direcionar esforços de marketing e expansão para as regiões mais promissoras.
&ensp;As colunas `Dim_Lojas.CANAL_VENDA`, `Dim_Lojas.REGIAO_CHILLI`, `Valor_Total` e `Quantidade` foram consideradas relevantes para essa análise. Mais detalhes podemos encontrar no notebook [graph1.ipynb](../notebooks/graph1.ipynb).

<div align="center">
<sub>Figura X - Gráfico de ticket médio por canal de vendas e por região</sub>

![Imagem_Ticket médio por canal de vendas e por região](../assets/Ticket_regiao_canal.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

---

##### 2. Volume de vendas por tipo de produto e agregado por faixa etária

&ensp;A análise da quantidade de produtos por tipo de produto e faixa etária permite entender as preferências de compra de diferentes grupos etários. Esta análise é fundamental para a Problemática 2: Definição do perfil ideal de cliente para cada uma das marcas, com vistas a orientar campanhas de comunicação personalizadas e aumentar a assertividade na captação de novos consumidores.
&ensp;Através da análise da quantidade de produtos por tipo de produto e faixa etária, é possível identificar as preferências de compra de diferentes grupos etários e entender se determinados segmentos apresentam maior aderência a categorias específicas.
&ensp;As colunas `Dim_Produtos.Grupo_Produto` e `Dim_Cliente.Data_Nascimento` foram consideradas relevantes para essa análise. Mais detalhes podemos encontrar no notebook [graph2.ipynb](../notebooks/graph2.ipynb).

<div align="center">
<sub>Figura X - Gráfico de distribuição de compras por faixa etária e grupo de produto</sub>

![Imagem_quantidade de produtos por tipo de produto e faixa etária](../assets/idade_quantida_tipo.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

---

##### 3. Volume de vendas por tipo de loja de ótica e por produto

&ensp;A análise do volume de vendas por tipo de loja de ótica e por produto permite entender a performance de cada tipo de loja em diferentes categorias de produtos. Esta análise é fundamental para a Problemática 3: Crescimento da categoria de óculos de grau, visando identificar oportunidades de expansão e estratégias para aumentar a penetração dessa linha de produtos.
&ensp;Através da análise do volume de vendas por tipo de loja de ótica e por produto, é possível identificar as tendências de consumo em diferentes tipos de lojas e comparar as vendas de óculos de grau em cada um desses contextos.
&ensp;As colunas `Dim_Lojas.Tipo_PDV`, `Dim_Produtos.Sub_Grupo` e `Quantidade` foram consideradas relevantes para essa análise. Mais detalhes podemos encontrar no notebook [graph3.ipynb](../notebooks/graph3.ipynb).

<div align="center">
<sub>Figura X - Gráfico de valor total de vendas por tipo de loja e produto</sub>

![Imagem_Valor total de vendas por tipo de loja e produto](../assets/valor_loja_produto.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

---

##### Feedbacks do Parceiro na Sprint Review 2

&ensp;O parceiro levantou algumas dúvidas sobre volume de vendas vs ticket médio em regiões diferentes.
&ensp;Durante a análise, encontrou-se outliers no tipo de loja 'Eco Chilli', que foram removidos do dataset pois tinham um volume de vendas de menos de 100 unidades, causando um **Ticket Médio 2x maior** no **Norte** que regiões conhecidamente mais ricas como a Sudeste.
&ensp;Isso demonstra a inconsistência dos dados, que impacta diretamente na confiabilidade dos insights gerados.
&ensp;Abaixo, você pode conferir o gráfico de volume de vendas vs ticket médio por região, após a remoção dos outliers.

&ensp;As colunas `Dim_Lojas.CANAL_VENDA`, `Dim_Lojas.REGIAO_CHILLI`, `Valor_Total` e `Quantidade` foram consideradas relevantes para essa análise. Mais detalhes podemos encontrar no notebook [graph1.ipynb](../notebooks/graph1.ipynb).

<div align="center">
<sub>Figura X - Gráfico de volume de vendas vs ticket médio por região</sub>
<img src="../assets/volume_ticket_regiao.png" scale="50%">

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;Além disso, o parceiro pediu uma comparação entre volume de vendas e lojas de rua vs lojas não de rua.
&ensp;Abaixo, você pode conferir o gráfico de volume de vendas por tipo de loja, após a remoção dos outliers.

&ensp;As colunas `Dim_Lojas.Tipo_PDV` e `Quantidade` foram consideradas relevantes para essa análise. Mais detalhes podemos encontrar no notebook [volume_por_tipo_pdv.ipynb](../notebooks/volume_por_tipo_pdv.ipynb).

<div align="center">
<sub>Figura X - Gráfico de volume de vendas por tipo de loja</sub>

![Imagem_Volume de vendas por tipo de loja](../assets/volume_tipo_de_loja.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;Além disso, o parceiro também pediu uma análise das cidades que menos performaram. Para esta análise, decidimos comparar o volume e ticket-médio da cidade vs a média do estado.
&ensp;Abaixo, você pode conferir o gráfico de cidades que menos performaram, após a remoção dos outliers.

&ensp;As colunas `Dim_Lojas.Cidade_Emp`, `Dim_Lojas.Estado_Emp`, `Valor_Total` e `Quantidade` foram consideradas relevantes para essa análise. Mais detalhes podemos encontrar no notebook [cidades_abaixo_media_estado.ipynb](../notebooks/cidades_abaixo_media_estado.ipynb).

<div align="center">
<sub>Figura X - Gráfico de cidades que menos performaram</sub>

<img src="../assets/cidade_abaixo_media.png" scale="50%">

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;Esta é uma ótima evidência, pois permite identificar cidades com potencial de crescimento, uma vez que elas possuem um volume de vendas abaixo da média do estado, mas um ticket médio acima da média do estado.
&ensp;Mas ainda estamos incertos dos resultados encontrados pela inconsistência dos dados apresentados pelo parceiro e comparar volume entre cidades pode ser problemático, por causa da baixa quantidade de dados após a filtragem.

---

#### Principais Insights

- &nbsp; A categoria de **óculos** manteve-se consistentemente como a mais vendida entre todas as faixas etárias. Essa distribuição segue um formato aproximadamente normal, com maior concentração de vendas na faixa etária **30 a 39 anos**.  

- &nbsp; As **Echo Chilli** apresentam um **ticket médio mais elevado** nas regiões **Norte e Nordeste**, indicando um potencial de valorização nesses mercados.  

- Já as lojas **Óticas** e **Vermelhas** apresentam desempenho mais **uniforme em todas as regiões**, sugerindo uma estabilidade maior no perfil de consumo desses canais.

- Filtrar por **volume de venda** mínimo é interessante, pois permite remover outliers que podem afetar o modelo preditivo, devido a inconsistência dos dados enviados pelo parceiro.

&ensp;Com essa análise, podemos entender melhor os dados e melhorar o nosso modelo preditivo. Entender os dados nos permite entender melhor o comportamento do cliente e, assim, criar um modelo mais preciso e eficiente.

#### <a name="c4.2.2"></a>4.2.2. Pré-processamento dos dados

##### O que é Pré-processamento de Dados?

&ensp;O pré-processamento de dados é como "preparar os ingredientes antes de cozinhar". Assim como um chef precisa lavar, cortar e temperar os ingredientes antes de fazer um prato, precisamos limpar, organizar e transformar os dados antes de criar modelos preditivos.

**Por que é necessário?**

- **Qualidade dos dados:** Dados "sujos" (com erros, valores faltantes ou inconsistências) produzem modelos imprecisos
- **Compatibilidade:** Diferentes algoritmos precisam que os dados estejam em formatos específicos
- **Performance:** Dados bem preparados fazem os modelos aprenderem melhor e mais rápido
- **Confiabilidade:** Resultados mais precisos e confiáveis para tomada de decisões de negócio

**Impacto na qualidade dos modelos:**
&ensp;Um modelo treinado com dados mal preparados é como tentar dirigir com o para-brisa sujo, você pode chegar ao destino, mas com muito mais risco de erro. O pré-processamento adequado pode melhorar a precisão dos modelos em 20-30% ou mais.

##### Metodologia Aplicada

&ensp;Antes de fazermos o pré-processamento, fizemos uma limpeza prévia no banco de dados, removendo registros inconsistentes e duplicados com o módulo [data_filtering.py](../data_filtering.py). Depois, aplicamos transformações nos dados com o módulo [data_preprocessing.py](../data_preprocessing.py) para torná-los adequados para nosso modelo preditivo.

**Implementação Prática:**

- **Notebook de Filtragem:** [filtering.ipynb](../notebooks/filtering.ipynb) - Documentação detalhada dos filtros aplicados
- **Notebook de Pré-processamento:** [proccess_data.ipynb](../notebooks/proccess_data.ipynb) - Implementação e visualização das transformações
- **Módulos Reutilizáveis:** Código modularizado para aplicação consistente em múltiplos notebooks

##### Filtragem Inicial dos Dados

&ensp;Dado a base de dados fornecida pela empresa parceira Chilli Beans, foram identificadas inconsistências e erros nos dados que comprometem a qualidade dos modelos preditivos. Por exemplo, foram encontrados registros de clientes com idades inválidas, preços de produtos inconsistentes e registros duplicados.

&ensp;Comparação de Ano de Nascimento antes e depois da limpeza:

<div align="center">
  <sub>Figura x - Ano de Nascimento Antes</sub><br>
  <img src="../assets/nascimento_antes.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

<div align="center">
  <sub>Figura x - Ano de Nascimento Depois</sub><br>
  <img src="../assets/nascimento_depois.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;Este é um exemplo de como a limpeza dos dados pode afetar a qualidade dos modelos preditivos. Portanto, implementamos uma série de filtros adicionais para garantir a qualidade dos dados. O módulo [data_filtering.py](../data_filtering.py) implementa filtros de negócio essenciais que reduziram o dataset de **40.291** para **18.367** registros (-54,41%), removendo:

- Transações de devolução de mercadoria
- Registros com preços inconsistentes
- Clientes com idades inválidas (fora do intervalo 18-100 anos)
- Duplicatas e registros com dados incompletos de lojas/produtos
- E mais...

&ensp;Abaixo, você pode conferir um resumo dos filtros aplicados e seu impacto no dataset:

| Filtro Aplicado | Registros Restantes | Registros Removidos | % Removido | % Restante |
|---|---|---|---|---|
| Dados iniciais | 40.291 | 0 | 0,00% | 100,00% |
| Remoção DEVOLUÇÃO DE MERCADORIA | 39.010 | 1.281 | 3,18% | 96,82% |
| Preço Varejo > 1 | 38.543 | 467 | 1,16% | 95,66% |
| Total Preço Varejo > 1 | 38.543 | 0 | 0,00% | 95,66% |
| Validação cálculo valor total | 38.543 | 0 | 0,00% | 95,66% |
| Idade entre 10 e 100 anos | 19.288 | 19.255 | 47,79% | 47,87% |
| Idade >= 18 anos no cadastro | 18.694 | 594 | 1,47% | 46,40% |
| Remoção duplicatas | 18.598 | 96 | 0,24% | 46,16% |
| Validação valor total (tolerância 2) | 18.598 | 0 | 0,00% | 46,16% |
| Lojas com Tipo PDV válido | 18.367 | 231 | 0,57% | 45,59% |
| Produtos com nome válido | 18.367 | 0 | 0,00% | 45,59% |
| Produtos com grupo válido | 18.367 | 0 | 0,00% | 45,59% |

&ensp;Resultado final: **18.367** registros, representando **45,59%** dos dados originais.
&ensp;Com esses filtros, garantimos a qualidade dos dados e eliminamos registros inconsistentes, preparando o dataset para modelagem preditiva. Mais detalhes sobre cada filtro podem ser encontrados no módulo [data_filtering.py](../data_filtering.py).

##### Pré-Processamento dos Dados

&ensp;Com o dataset filtrado, seguimos uma metodologia rigorosa para preparar-lo para a modelagem preditiva. O módulo [data_preprocessing.py](../data_preprocessing.py) implementa as seguintes transformações:

**1. Tratamento de Valores Ausentes**

**O que são valores ausentes?**
&ensp;Valores ausentes (ou "missing values") são informações que deveriam estar presentes nos dados mas estão faltando. É como ter fichas de clientes incompletas, algumas podem estar sem telefone, outras sem endereço. Esses "buracos" nos dados podem prejudicar a análise.

&ensp;Foram identificadas 10 colunas com valores ausentes, totalizando 13.701 valores faltantes. As estratégias aplicadas foram:

| Tipo de Coluna | Estratégia | Justificativa |
|---|---|---|
| Colunas com >50% ausentes | Remoção da coluna | Informação insuficiente para modelagem |
| Categóricas | Imputação com moda | Preserva distribuição original |
| Numéricas | Imputação com mediana | Robusta a outliers |
| Outros tipos | Remoção de registros | Quando imputação não é apropriada |

<div align="center">
  <sub>Figura x - Tratamento de Valores Ausentes</sub><br>
  <img src="../assets/valores_ausentes.png">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

**2. Detecção e Tratamento de Outliers**

**O que são outliers?**
&ensp;Outliers são valores "estranhos" ou extremos nos dados, como encontrar uma pessoa de `2,50m` de altura numa pesquisa sobre brasileiros (a média é `1,70m`). Esses valores podem ser erros de digitação ou casos realmente excepcionais, mas podem "confundir" os modelos preditivos.

**Exemplo prático:** Se a maioria dos produtos custa entre `R$ 50-200`, mas alguns custam `R$ 50.000`, esses valores extremos são outliers.

**O que é IQR (Interquartile Range)?**
&ensp;O IQR é uma técnica estatística que funciona como um "detector de valores estranhos". Imagine que você organize todos os valores em ordem crescente e os divida em 4 grupos iguais:

- **Q1 (1º quartil):** 25% dos dados estão abaixo deste valor
- **Q3 (3º quartil):** 75% dos dados estão abaixo deste valor
- **IQR = Q3 - Q1:** A "faixa normal" onde estão 50% dos dados centrais

**Fórmula de detecção:**

- **Limite inferior:** $Q_1 - 1.5 × IQR$
- **Limite superior:** $Q_3 + 1.5 × IQR$
- **Outliers:** Valores fora destes limites

Utilizamos o método IQR para detectar outliers nas principais colunas numéricas. Conforme implementado no notebook [proccess_data.ipynb](../notebooks/proccess_data.ipynb), o processamento removeu **499 registros** (2,72%) devido a outliers extremos, aplicando estratégias adaptativas:

| Coluna | Outliers Detectados | Percentual | Tratamento Aplicado |
|---|---|---|---|
| Quantidade | $1.536$ | $8.36$% | Substituição por percentis 5% e 95% |
| Preco_Custo | $402$ | $2.19$% | Remoção de registros extremos |
| Valor_Total | $373$ | $2.09$% | Remoção de registros extremos |
| Preco_Varejo | $174$ | $0.95$% | Mantidos (impacto negligível) |
| Frete | $2$ | $0.01$% | Mantidos (impacto negligível) |
| Desconto | $2.647$ | $14.41$% | Cap truncation aplicado |

**Critérios de Tratamento:**

**Cap Truncation (Winsorização):** É como "cortar as pontas" dos valores extremos. Em vez de remover os outliers, limitamos eles aos valores máximo e/ou mínimo aceitáveis. É como dizer: "se alguém declarou idade de 200 anos, vamos considerar 100 anos (o máximo razoável)".

- **>8% outliers:** Cap truncation para preservar informação (muitos outliers = podem ser válidos)
- **5-8% outliers:** Substituição por percentis 5% e 95% (valores menos extremos)
- **1-5% outliers:** Remoção de registros extremos (poucos casos = provavelmente erros)
- **<1% outliers:** Manutenção (impacto pequeno nos resultados)

<div align="center">
  <sub>Figura x - Remoção de Outliers</sub><br>
  <img src="../assets/outliers.png">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

**3. Normalização e Padronização**

**Por que transformar a escala dos dados?**
&ensp;Imagine comparar a altura de pessoas (em metros: 1,70) com seus salários (em reais: 5.000). Os algoritmos podem dar mais importância aos salários simplesmente porque os números são maiores. É como comparar maçãs com elefantes, precisamos colocar tudo na mesma "unidade de medida".

**Normalização Min-Max (0-1):**
&ensp;Transforma todos os valores para uma escala de 0 a 1, como converter notas de diferentes provas (0-10, 0-100, 0-20) todas para uma escala de 0 a 1.

**Fórmula:** $\frac{(x - min)}{(max - min)}$

- **x:** valor original
- **min:** menor valor da coluna
- **max:** maior valor da coluna
- **Resultado:** Todos os valores ficam entre 0 e 1

**Padronização Z-Score (μ=0, σ=1):**
&ensp;Transforma os dados para ter média zero e desvio padrão 1. É como "centralizar" os dados em torno da média. Serve para comparar variáveis com escalas diferentes, como idade (anos) e renda (reais).

**Fórmula:** $\frac{(x - \mu)}{\sigma}$

- **x:** valor original
- **μ (mu):** média dos valores
- **σ (sigma):** desvio padrão
- **Resultado:** Média = 0, desvio padrão = 1

<div align="center">
  <sub>Figura x - Visualização Gráfica do Escalonamento</sub><br>
  <img src="../assets/escalonamento.png">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

**4. Codificação de Variáveis Categóricas**

**Por que codificar variáveis categóricas?**
&ensp;Computadores entendem números, não palavras. É como traduzir "São Paulo", "Rio de Janeiro", "Minas Gerais" para uma linguagem que os algoritmos conseguem processar.

**One-Hot Encoding (Variáveis Nominais):**
&ensp;Usado quando as categorias não têm ordem natural (como: primeiro, segundo, terceiro). Cria uma coluna chamada "dummy" para cada categoria com valores 0 ou 1.
&ensp;Colunas dummy são variáveis binárias (0 ou 1) que representam a presença ou ausência de uma categoria específica. O termo "dummy" significa "fictício" ou "artificial" - são colunas criadas artificialmente para representar informações categóricas em formato numérico.

**Exemplo:** Estado Civil

- Original: ["Solteiro", "Casado", "Divorciado"]
- Após One-Hot:
  - Solteiro: $[1, 0, 0]$
  - Casado: $[0, 1, 0]$
  - Divorciado: $[0, 0, 1]$

**Label Encoding (Variáveis Ordinais):**
Usado quando as categorias têm ordem natural (como tamanhos: P, M, G). Atribui números sequenciais.

**Exemplo:** Tamanho

- Original: $["P", "M", "G"]$
- Após Label: $P=1, M=2, G=3$

**One-Hot Encoding (Variáveis Nominais):**

- `Dim_Lojas.Tipo_PDV`: 15 categorias → 14 colunas dummy
- `Dim_Lojas.CANAL_VENDA`: 3 categorias → 2 colunas dummy
- `Dim_Lojas.REGIAO_CHILLI`: 5 categorias → 4 colunas dummy
- `Dim_Cliente.Uf_Cliente`: 27 categorias → 26 colunas dummy
- `Dim_Produtos.Grupo_Produto`: 12 categorias → 11 colunas dummy
- `Dim_Produtos.Sub_Grupo`: 40 categorias → Top 10 + "Outros" → 10 colunas dummy

**Label Encoding (Variáveis Ordinais):**

- `Dim_Cliente.Sexo`: $M=1, F=0, S=2, ""=3$
- `Dim_Cliente.Estado_Civil`: Mapeamento automático por frequência

##### Resultados do Pré-processamento

Conforme documentado nos notebooks [filtering.ipynb](../notebooks/filtering.ipynb) e [proccess_data.ipynb](../notebooks/proccess_data.ipynb):

| Métrica | Antes | Depois |
|---|---|---|
| **Registros** | $40.291$ | $17.868$ |
| **Colunas** | $66$ | $126$ (+60 novas) |
| **Valores Ausentes** | $13.701$ | $0$ |
| **Outliers Tratados** | $4.311$ | $499$ registros removidos |
| **Variáveis Normalizadas** | $0$ | $6$ colunas (Min-Max + Z-Score) |
| **Variáveis Codificadas** | $0$ | $8$ variáveis categóricas → $48$ colunas dummy |
| **Retenção Final** | - | $44.35$% dos dados originais |

##### Impacto do Pré-processamento

<div align="center">
  <sub>Figura x - Visão Geral da Transformação dos Dados</sub><br>
  <img src="../assets/proccessing_resume.png">
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

**Benefícios Alcançados:**

1. **Qualidade dos Dados:** Eliminamos $13.701$ valores ausentes e $4.311$ outliers que poderiam gerar previsões incorretas
2. **Consistência:** Todas as variáveis numéricas agora estão na mesma escala, evitando que algumas dominem outras
3. **Compatibilidade:** Variáveis categóricas foram convertidas para formato numérico, permitindo uso em qualquer algoritmo
4. **Confiabilidade:** Dados limpos e padronizados resultam em modelos mais precisos e confiáveis

&ensp;Concluindo, o pré-processamento dos dados teve um impacto significativo na qualidade e confiabilidade dos modelos preditivos, preparando-os para fornecer insights valiosos e estratégicos para a Chilli Beans.

#### <a name="c4.2.3"></a>4.2.3. Hipóteses

Nesta seção são descritas as hipóteses de pesquisa formuladas a partir das problemáticas levantadas.  
O objetivo é verificar, por meio de análises estatísticas, se existem relações significativas entre variáveis relevantes do negócio e os resultados observados.  
Cada hipótese foi estruturada com definição do teste estatístico, justificativa da formulação e interpretação dos resultados, de modo a apoiar a tomada de decisão com base em evidências quantitativas.  

---

### Hipótese para problemática 1 — Fluxo e Conversão das Óticas de Rua  

&ensp;As lojas de rua são um modelo novo e podem apresentar comportamento de vendas distinto em relação a shoppings, quiosques e formato Eco. Validar estatisticamente essa hipótese é essencial para compreender a performance de cada canal e orientar estratégias comerciais.  

**Hipótese de Pesquisa**  

- H₀: O tipo de PDV **influencia** a quantidade vendida.  
- H₁: O tipo de PDV **não influencia** significativamente a quantidade vendida.

**Evidência**  
<div align="center">
<sub>Figura X - Output hipótese 1

![Hipótese 1](../assets/secao-4-2-3/hypothesis1.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

**Interpretação**  
O teste ANOVA indicou diferença significativa entre os tipos de PDV (p < 0.001).  
Dessa forma, a hipótese alternativa (H₁) foi rejeitada e a hipótese nula (H₀) aceita, concluindo-se que o tipo de PDV influencia a quantidade vendida.  
Verificou-se que as lojas de rua, especialmente aquelas com perfil ótico, apresentam em média maior número de unidades vendidas por compra em comparação aos demais canais, o que sugere um comportamento distinto nesse formato.  

---

### Hipótese para problemática 2 — Público Ideal  

&ensp;A hipótese de pesquisa propõe avaliar se existe uma associação entre a idade do cliente e o ticket médio, visando identificar se determinados grupos geracionais concentram maior valor de compra.

**Hipótese de Pesquisa**  

- H₀: A idade do cliente **está associada** ao ticket médio.  
- H₁: A idade do cliente **não está associada** ao ticket médio.  

**Evidência**  
<div align="center">
<sub>Figura X - Output hipótese 2

![Hipótese 2](../assets/secao-4-2-3/hypothesis2.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

**Interpretação**  
&ensp;O teste ANOVA indicou diferença significativa entre as faixas etárias (p < 0.001).  
&ensp;Dessa forma, a hipótese alternativa (H₁) foi rejeitada e a hipótese nula (H₀) aceita, concluindo-se que a idade do cliente está associada ao ticket médio.

---

### Hipótese para problemática 3 — Sortimento e Estoque

&ensp;Analisar se o impacto das Óticas Chilli Beans é o mesmo entre as diferentes localidades. Dessa forma, verificar se a participação das vendas de lentes de grau difere entre localidades (UF/cidade) para orientar decisões de sortimento, estoque e campanhas regionais.

&ensp;Aplicaremos um teste qui-quadrado para avaliar a independência entre as variáveis "localidade" e "tipo de lente" (grau ou solar). O teste qui-quadrado funciona comparando a distribuição observada com a distribuição esperada sob a hipótese de independência.

**Hipótese de Pesquisa**  

- H₀: A participação de lentes de grau nas vendas **é a mesma** entre estados/cidades.  
- H₁: A participação de lentes de grau **varia** por estado/cidade.  

**Evidência**  

<div align="center">
<sub>Figura X - Output hipótese 3

![Hipótese 3](../assets/secao-4-2-3/hypothesis3.png)

<sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

**Interpretação**  

O teste qui-quadrado indicou diferença significativa na distribuição das vendas entre as localidades (p < 0.05).
Isso nos leva a rejeitar H₀ e aceitar H₁: a participação de lentes de grau nas vendas varia de acordo com o estado/cidade.
Observamos que algumas localidades apresentam maior proporção de vendas de lentes de grau, enquanto outras concentram-se mais em óculos solares, sugerindo que o comportamento de consumo não é uniforme e que há necessidade de estratégias regionais de sortimento e marketing.

---

### Conclusão Geral

&ensp;Estas hipóteses foram formuladas a partir das problemáticas identificadas e validadas por meio de análises estatísticas.  
&ensp;Os resultados obtidos apontam para diferenças significativas entre os tipos de PDV, faixas etárias e localidades, indicando que o comportamento de consumo não é uniforme e que há necessidade de estratégias personalizadas para cada segmento.

### <a name="c4.3"></a>4.3. Preparação dos Dados e Modelagem

&ensp;Esta seção apresenta a construção de dois modelos híbridos (combinação estruturada de etapas não supervisionadas e supervisionadas) orientados a apoiar o crescimento da categoria de grau sob duas perspectivas complementares: (i) cliente individual e (ii) território (cidades). O objetivo estratégico é priorizar onde investir esforço comercial e orçamento (nível cliente) e onde alocar capital para expansão ou reforço de operação (nível cidade), reduzindo risco de decisões baseadas apenas em intuição.

&ensp;Chamamos de modelo híbrido, aqui, a integração de: (1) uma etapa de clusterização ("clusterização": técnica não supervisionada que agrupa registros com padrões semelhantes sem rótulos prévios) para estruturar perfis; (2) um modelo supervisionado ("modelo supervisionado": algoritmo treinado com exemplos rotulados para prever uma variável de interesse) que gera um score contínuo de potencial/performance; e (3) um módulo de recomendação/ranqueamento ("ranqueamento": ordenação dos itens com base em regras e/ou scores) que transforma resultados analíticos em ação operacional (priorização de clientes ou cidades).

&ensp;Os dois notebooks de referência são: `hybrid_model_1.ipynb` (foco micro: perfis e potencial de clientes para direcionar campanhas e esforços de relacionamento) e `hybrid_model_2.ipynb` (foco territorial: avaliação comparativa de cidades para orientar abertura ou reforço de óticas). As subseções a seguir detalham fundamentos comuns, particularidades de cada fluxo e evidências técnicas (features, definição de K, métricas e scores) conforme organizado no sumário.

#### <a name="c4.3.1"></a>4.3.1 Visão Geral dos Dois Modelos Híbridos

&ensp; A problemática, em questão, aborda o desafio do crescimento da categoria de óculos de grau da Chilli Beans. Embora a marca seja amplamente reconhecida por suas armações e pela força no segmento de óculos solares, a penetração da linha de lentes de grau ainda é limitada. Para reverter esse quadro, é necessário compreender de forma aprofundada o comportamento de consumo dos clientes, identificar barreiras de conversão e, principalmente, construir estratégias que aumentem a relevância da marca nesse segmento.

&ensp; A solução proposta é estruturada em duas frentes paralelas. No nível micro (cliente), o foco está em estimar a propensão de compra de lentes de grau por perfil individual de cliente. Com base em dados transacionais e características sociodemográficas, um modelo supervisionado é treinado para prever a possibilidade de que cada consumidor adquira produtos da categoria de grau. Essa predição permite priorizar esforços comerciais e campanhas de CRM, direcionando abordagens personalizadas para clientes com maior chance de conversão. Dessa forma, a empresa consegue atuar de forma mais cirúrgica, aproveitando o potencial de clientes já expostos à marca e reduzindo barreiras entre a compra de armações e a aquisição das lentes de grau.

&ensp; No nível territorial (cidade), o foco é estratégico: otimizar a alocação de capital em praças com maior retorno previsto. Através da análise de indicadores de vendas, canais e perfis regionais, modelos preditivos classificam cidades e lojas segundo seu potencial de crescimento da categoria de grau. Isso permite à empresa tomar decisões informadas sobre reforçar pontos de venda existentes ou planejar novas aberturas em regiões promissoras, garantindo eficiência nos investimentos e maximizando retorno em médio e longo prazo.

&ensp; Para unir essas duas dimensões, adota-se uma lógica híbrida de modelagem. Em um primeiro momento, um modelo preditivo não supervisionados ajuda a estruturar perfis de clientes e territórios separadamente, revelando padrões ocultos e grupos com comportamentos semelhantes, dessa forma, dividindo-os em clusters. Em seguida, o modelo supervisionado gera scores contínuos de propensão de compra, tanto no nível de cliente quanto de cidade. Por fim, os resultados são operacionalizados por meio de um módulo de recomendação e ranking, que orienta as decisões de priorização: quais perfis de clientes devem receber campanhas mais intensivas, quais canais ou praças devem ser priorizadas em planos de expansão.

&ensp;Para acessar os notebooks que implementam os modelos híbridos, utilize os links abaixo:

- [Modelo Híbrido 1: Clientes → Investimento](/notebooks/hybrid_model_1.ipynb)
- [Modelo Híbrido 2: Cidades → Expansão de Óticas](/notebooks/hybrid_model_2.ipynb)

#### <a name="c4.3.2"></a>4.3.2 Fundamentos Técnicos

&ensp;Apesar de abordarem unidades de análise distintas (cliente e cidade), os dois modelos híbridos compartilham uma base metodológica comum para garantir consistência, reprodutibilidade e comparabilidade dos resultados. Esses fundamentos técnicos são aplicados em ambos os fluxos e representam as melhores práticas adotadas no projeto.

- **Fonte de Dados e Pré-processamento Centralizado**: Ambos os modelos partem de uma base de dados unificada, que passa por um rigoroso processo de limpeza e filtragem através do módulo `data_filtering.py`. Isso garante que regras de negócio críticas como remoção de devoluções, tratamento de inconsistências e validação de dados de clientes e produtos, sejam aplicadas de forma padronizada. A consistência na fonte de dados é crucial para a validade de qualquer análise comparativa posterior.

- **Escalonamento de Features com `StandardScaler`**: Para os algoritmos sensíveis à escala, como o K-Means (usado em ambos os modelos para clusterização), foi empregada a técnica de padronização com `StandardScaler`. Essa técnica transforma as features para que tenham média zero e desvio padrão um. A justificativa para sua escolha é a robustez no tratamento de outliers e a capacidade de preservar a informação da distribuição original dos dados, sendo fundamental para que o algoritmo de clusterização não seja enviesado por variáveis de grande magnitude, como `Monetaridade_Total`.

- **Prevenção de Vazamento de Dados (Data Leakage)**: Para garantir que os modelos supervisionados generalizem bem para dados não vistos, foram adotadas estratégias rigorosas de separação de dados.

  > **O que é Vazamento de Dados (Data Leakage)?**
  > É um erro que ocorre quando o modelo, durante seu treinamento, tem acesso a informações que não estariam disponíveis em um cenário real de previsão. É como dar a um aluno as respostas da prova antes do teste. O modelo "decora" os resultados em vez de aprender a raciocinar, o que o torna ineficaz com dados novos. Para evitar isso, separamos os dados em conjuntos de **treino** (para aprender) e **teste** (para avaliar com dados inéditos).

  - No modelo de cidades (`hybrid_model_2.ipynb`), utilizou-se `train_test_split` com **estratificação** pelos **quartis** da variável alvo (`performance_score`).

    > **O que são Quartis?**
    > Quartis são valores que dividem um conjunto de dados ordenado em quatro partes iguais. Pense em uma fila de cidades organizadas por performance:
    > - **Primeiro Quartil (Q1):** Separa as 25% cidades com menor performance.
    > - **Segundo Quartil (Q2):** É a mediana, o valor que divide a fila ao meio (50%).
    > - **Terceiro Quartil (Q3):** Separa as 75% cidades com menor performance das 25% com maior performance.
    > Usamos os quartis para criar grupos de performance (baixo, médio-baixo, médio-alto, alto).

    > **O que é Estratificação?**
    > É uma técnica para garantir que, ao dividir os dados em treino e teste, ambos os conjuntos mantenham a mesma proporção dos grupos definidos pelos quartis. Se 25% das cidades têm alta performance no total de dados, a estratificação garante que os conjuntos de treino e teste também tenham, cada um, 25% de cidades de alta performance. Isso torna a avaliação do modelo mais justa e confiável.

  - No modelo de clientes (`hibrido_clientes.ipynb`), a abordagem é similar, garantindo que a performance do modelo seja avaliada em um conjunto de dados que ele nunca viu durante o treinamento.

- **Estratégia de Seleção de Métricas de Avaliação**: A escolha das métricas foi alinhada aos objetivos de cada etapa da modelagem:
  - **Modelos Não Supervisionados (Clusterização)**: Para determinar o número ideal de clusters (K) e avaliar sua qualidade, foram utilizadas múltiplas métricas:
    > **O que são Métricas de Clusterização?**
    > São ferramentas que nos ajudam a responder duas perguntas: "Quantos grupos (clusters) devemos criar?" e "Esses grupos fazem sentido?". Elas avaliam se os membros de um grupo são parecidos entre si e diferentes dos membros de outros grupos.

    - **Método do Cotovelo (Inércia/WCSS)**: Ajuda a identificar o ponto onde adicionar mais clusters não melhora significativamente a compactação interna dos grupos. É como organizar uma estante: no início, cada nova prateleira ajuda muito, mas depois de um certo ponto, adicionar mais prateleiras não organiza muito mais os livros. O "cotovelo" é o ponto de equilíbrio ideal.
    - **Silhouette Score**: Mede quão "confortável" um ponto de dados está em seu próprio cluster em comparação com os clusters vizinhos. Um score alto significa que os clusters estão bem definidos e separados.
    - **Calinski-Harabasz Score**: Avalia a razão entre a dispersão entre clusters e a dispersão dentro de cada cluster. Quanto maior o valor, mais densos e bem separados são os clusters.
    - **Davies-Bouldin Score**: Mede a similaridade média de cada cluster com seu vizinho mais próximo. Um score baixo indica que os clusters estão bem separados.

  - **Modelos Supervisionados**:
    - **Regressão (Score de Performance de Cidades)**: Usado para prever um valor numérico contínuo, como a nota de performance de uma cidade.
      > **O que é Validação Cruzada (Cross-Validation)?**
      > É uma técnica para testar a estabilidade do modelo. Em vez de dividir os dados apenas uma vez em treino e teste, fazemos isso várias vezes (por exemplo, 5 ou 10), usando diferentes partes dos dados para treinar e testar. A média dos resultados nos dá uma estimativa mais robusta e confiável do desempenho do modelo em dados do mundo real.

      - **R² (Coeficiente de Determinação)**: Mede o quão bem o modelo explica a variação nos dados. Um R² de 0.75 significa que o modelo consegue explicar 75% do comportamento da variável que estamos tentando prever.
      - **RMSE** (Raiz do Erro Quadrático Médio) e **MAE** (Erro Médio Absoluto): Ambas medem a magnitude média do erro das previsões. A diferença é que o RMSE penaliza mais os erros grandes. São como réguas que nos dizem, em média, "o quão longe" as previsões do modelo estão dos valores reais.

    - **Classificação (Propensão de Compra de Grau)**: Usado para prever uma categoria, como "vai comprar" ou "não vai comprar".
      - **Acurácia**: A métrica mais simples. Mede a porcentagem de previsões corretas que o modelo fez no total.
      - **Matriz de Confusão**: Uma tabela que mostra os acertos e erros do modelo em detalhe. Ela nos diz quantos "vai comprar" foram previstos corretamente, quantos foram confundidos com "não vai comprar", e vice-versa.
      - **Relatório de Classificação**:
        - **Precisão (Precision)**: De todos que o modelo disse que "vão comprar", quantos realmente compraram? É uma medida de "confiança" na previsão positiva.
        - **Recall (Sensibilidade)**: De todos que realmente compraram, quantos o modelo conseguiu identificar? É uma medida de "abrangência", para não deixar passar oportunidades.
        - **F1-Score**: É a média harmônica entre Precisão e Recall. Útil para encontrar um equilíbrio entre as duas, especialmente quando as classes estão desbalanceadas (ex: muito mais gente que "não compra" do que "compra").

- **Modularização e Reprodutibilidade**: Ambos os notebooks importam funções de scripts Python externos (`data_filtering.py`, `data_preprocessing.py`), o que centraliza a lógica de transformação de dados e garante que as mesmas operações sejam executadas em diferentes análises. O uso de `random_state=42` em todos os algoritmos estocásticos (K-Means, `train_test_split`, modelos de árvore) assegura que os resultados sejam 100% reprodutíveis.

Cada notebook foi estruturado para garantir reprodutibilidade e clareza. O notebook [hybrid_model_1.ipynb](/notebooks/hybrid_model_1.ipynb) foca na segmentação e predição de clientes, enquanto o [hybrid_model_2.ipynb](/notebooks/hybrid_model_2.ipynb) aborda a análise territorial. Ambos utilizam funções modularizadas para pré-processamento e validação, garantindo consistência entre as análises.

---

#### <a name="c4.3.3"></a>4.3.3 Modelo Híbrido 1 (Clientes → Investimento)

&ensp; O objetivo do modelo é priorizar clientes para investimento na categoria “grau”, tanto em ações de **upsell** (clientes ativos com potencial de aumentar o consumo) quanto de **reativação** (clientes inativos ou em risco de churn).

- **Alvo supervisionado:** a variável preditiva é a propensão do cliente a adquirir produtos de **grau** no horizonte futuro. Isso foi operacionalizado como `propensidade_grau`, calculada a partir do modelo final aplicado sobre o perfil histórico dos clientes.
- **Janela de observação (lookback):** o histórico de transações foi agregado até a data de referência (máxima encontrada no dataset), calculando métricas de comportamento, frequência e valor.
- **Horizonte preditivo:** embora o código não fixe explicitamente, a modelagem busca estimar a probabilidade de compra futura de grau no período subsequente à janela de observação.
- **Unidade de modelagem:** cliente individual (`ID_Cliente`).
- **Valor de negócio do score:** o ranking de propensão (`propensidade_grau`) permite alocar verba incremental de marketing em campanhas direcionadas, priorizando os clientes com maior potencial de conversão na categoria.
- **Critérios de exclusão:**

  - Clientes em cidades com menos de 5 clientes únicos (filtragem de representatividade).
  - Clientes sem histórico suficiente para cálculo de features (ex.: apenas uma compra, resultando em cadência infinita).
  - Novos clientes sem tempo mínimo de observação (`Tempo_Como_Cliente` muito baixo).

&ensp;Este modelo foi implementado no notebook [hybrid_model_1.ipynb](/notebooks/hybrid_model_1.ipynb)

#### <a name="c*******"></a>******* Modelagem do Problema - Aprendizado Não Supervisionado

&ensp; O problema de clustering foi modelado como uma tarefa de segmentação comportamental, onde o objetivo é agrupar clientes com padrões similares de consumo, valor e engajamento. Esta segmentação permite identificar personas e comportamentos estruturais que influenciam a propensão de compra de óculos de grau.

&ensp;  A clusterização antecede a classificação por dois motivos estratégicos: permite identificar perfis latentes de comportamento que não são evidentes através de variáveis demográficas tradicionais; a heterogeneidade comportamental entre clientes (diferenças de frequência, valor, canal) precisa ser capturada antes da modelagem preditiva para melhorar a granularidade das campanhas de marketing.

&ensp;A engenharia de features foi realizada com foco em capturar dimensões críticas do comportamento do cliente. Por exemplo, a variável `Cadencia_Compra` foi derivada para medir a regularidade de compras, enquanto `Pct_Compras_Canal_Otica` reflete a afinidade do cliente com o canal ótico. Além disso, variáveis como `Ticket_Medio` e `Preco_Medio_Item` foram criadas para avaliar o perfil de gasto. Essas features foram selecionadas com base em hipóteses de negócio e validadas estatisticamente para garantir sua relevância no modelo.

##### Construção das Features

A função `cria_perfil_historico_cliente` gera um conjunto de features agregadas por cliente, cobrindo diferentes dimensões de comportamento:

1. **RFM (Recência, Frequência, Monetaridade):**

   - `Recencia` = dias desde a última compra.
   - `Frequencia` = número de transações distintas (`DOC_UNICO`).
   - `Monetaridade_Total` = soma de `Valor_Total`.
   - **Hipótese:** clientes mais recentes, frequentes e de maior gasto têm maior probabilidade de voltar a comprar grau.

2. **Mix de Categoria:**

   - Flags históricas (`Comprou_Grau_Historico`, `Comprou_Solar_Historico`, `Comprou_Relogio_Historico`).
   - Percentuais de compras em canais ou tipos de loja (`Pct_Compras_Canal_Otica`, `Pct_Compras_Loja_Rua`, `Pct_Compras_Quiosque`, `Pct_Compras_Loja_Shopping`).
   - **Hipótese:** clientes já engajados com grau ou que compram em canais específicos têm mais afinidade com a categoria.

3. **Intensidade de Desconto:**

   - `Pct_Compras_Com_Desconto` = proporção de transações com desconto.
   - **Hipótese:** sensibilidade ao desconto pode indicar propensão a converter mediante campanhas promocionais.

4. **Diversidade de Lojas:**

   - `N_Lojas_Unicas` = número de lojas distintas visitadas.
   - **Hipótese:** clientes que compram em mais lojas são mais engajados e de maior valor potencial.

5. **Ciclo Temporal:**

   - `Dia_Semana_Preferido` = moda dos dias da semana das compras.
   - **Hipótese:** padrões temporais ajudam a segmentar clientes (ex.: compradores de fim de semana).

6. **Engajamento e Comportamento de Compra:**

   - `Itens_Por_Transacao` = quantidade média por compra.
   - `Ticket_Medio` = média de `Valor_Total`.
   - `Preco_Medio_Item` = monetaridade dividido pela quantidade.
   - `Cadencia_Compra` = tempo como cliente dividido pelo nº de compras – 1.
   - **Hipótese:** maior cadência e ticket médio estável indicam relacionamento contínuo e previsível.

7. **Demográfico derivado:**

   - `Idade` do cliente (calculada pela data de nascimento).
   - **Hipótese:** diferentes faixas etárias podem ter padrões de consumo distintos em grau.

**Tratamento de outliers e dados faltantes:**

- Valores infinitos em `Cadencia_Compra` (clientes com apenas 1 compra) foram substituídos por `999`.
- Colunas com `NaN` em `Idade` foram eliminadas para evitar inconsistências.

**Features descartadas:**

- Datas originais (`Data_Primeira_Compra`, `Data_Ultima_Compra`, `Data_Nascimento`) foram excluídas após a criação das features derivadas.
- Features redundantes ou com baixa variância poderiam ser removidas na etapa de seleção final do modelo (ex.: alta correlação entre `Ticket_Medio` e `Preco_Medio_Item`).

##### Seleção de Características (Features)

&ensp;O processo de clusterização exige a escolha criteriosa de variáveis que representem de forma abrangente os diferentes aspectos do comportamento do cliente. Para este estudo, foram selecionadas 12 variáveis, divididas em quatro categorias:

###### Variáveis Financeiras

- **Monetaridade_Total**: valor absoluto gasto pelo cliente ao longo do histórico. Representa a relevância econômica de cada cliente.  
- **Ticket_Medio**: média por transação, que captura o poder de compra individual.  
- **Preco_Medio_Item**: valor médio por item adquirido, indicador de sensibilidade a preços.  

###### Variáveis de Frequência

- **Frequencia**: número total de compras, reflete a intensidade do relacionamento.  
- **Cadencia_Compra**: intervalo médio entre compras, medindo a regularidade do consumo.  
- **Recencia**: tempo desde a última compra, fundamental para identificar clientes ativos ou em risco de churn.  

###### Variáveis de Canal de Venda

- **Pct_Compras_Canal_Otica**: proporção de compras realizadas em óticas físicas.  
- **Pct_Compras_Loja_Shopping**: percentual de compras em lojas de shopping.  
- **Pct_Compras_Quiosque**: participação dos quiosques no mix de canais.  

Essas variáveis permitem entender a preferência por canais e a omnichannelidade do cliente.

###### Outras Variáveis Relevantes

- **N_Lojas_Unicas**: diversidade de pontos de compra.  
- **Tempo_Como_Cliente**: tempo de relacionamento desde a primeira compra.  
- **Idade**: idade do cliente, variável demográfica de apoio.  

A combinação dessas variáveis visa capturar três dimensões críticas: **valor econômico**, **comportamento de compra** e **preferência de canal**.

#### <a name="c*******"></a>******* Clusterização de Clientes e Definição de K

&ensp; A determinação do número ótimo de clusters (K) é fundamental para equilibrar granularidade dos perfis com viabilidade comercial. Valores baixos de K resultam em segmentos muito amplos (baixa personalização), enquanto valores altos fragmentam excessivamente a base.

###### Métricas Técnicas Utilizadas

&ensp;Foram testados valores de K entre 2 e 10, utilizando uma combinação de métricas:

| Métrica | Descrição | Interpretação |
|---------|-----------|---------------|
| **Método do Cotovelo (WCSS)** | Soma dos quadrados intra-cluster | Menor é melhor (busca-se o "cotovelo") |
| **Silhouette Score** | Qualidade da separação entre clusters | Varia de -1 a +1 (maior é melhor) |

###### Critérios Práticos de Negócio

- Cada cluster deveria conter **pelo menos 3% da base de clientes**, garantindo representatividade.  
- O número máximo de grupos foi limitado a **8**, para assegurar viabilidade na gestão comercial.  
- Os clusters deveriam ser **distintos e acionáveis**, ou seja, capazes de subsidiar estratégias diferentes de marketing e vendas.  

A combinação de critérios técnicos e pragmáticos permitiu balancear robustez estatística com aplicabilidade no negócio.

###### Resultados da Validação

**Análise do Silhouette Score por Número de Clusters:**

| K | Silhouette Score | Interpretação |
|---|------------------|---------------|
| 2 | 0.2847 | Separação boa, mas pouco granular |
| 3 | 0.2635 | Separação moderada |
| 4 | 0.2543 | Separação razoável |
| 5 | 0.2398 | Separação moderada |
| **6** | **0.2415** | **Melhor compromisso** |
| 7 | 0.2287 | Separação em declínio |
| 8 | 0.2156 | Fragmentação excessiva |

**Baseado na análise combinada das métricas, foi selecionado K=6 como número ótimo:**

- **Silhouette Score = 0.2415**: Indica separação moderada mas adequada dos clusters
- **Viabilidade comercial**: 6 segmentos permitem estratégias diferenciadas sem complexidade excessiva
- **Representatividade**: Cada cluster mantém pelo menos 3% da base de clientes

<div align="center">
  <sub>Figura x - Gráfico do Silhouete</sub><br>
  <img src="../assets/modelo-1/silhouete.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

#### <a name="c*******"></a>******* Caracterização dos Clusters Identificados

&ensp; Após a segmentação comportamental via K-Means, foram identificados 6 clusters de clientes com base na análise do Silhouette Score. A tabela abaixo resume as principais métricas de cada segmento:

| Cluster | % da Base | Silhouette Individual | Propensão Grau | Ticket Médio | Canal Preferido | Qualidade |
|---------|-----------|----------------------|----------------|--------------|-----------------|-----------|
| 4       | 0.4%      | 0.5392               | 19.2%          | R$ 897       | Óticas (65%)    | EXCELENTE |
| 5       | 13.2%     | 0.3272               | 15.3%          | R$ 949       | Multi-canal     | BOA |
| 0       | 81.2%     | 0.1876               | 8.7%           | R$ 304       | Shopping (97%)  | RAZOÁVEL |
| 1       | 4.8%      | 0.1654               | 5.1%           | R$ 310       | Loja Rua (100%) | RAZOÁVEL |
| 2       | 5.2%      | 0.0847               | 4.8%           | R$ 293       | Quiosque (100%) | PROBLEMÁTICA |
| 3       | 0.0%      | -0.0123              | -              | -            | -               | MUITO PROBLEMÁTICA |

**Interpretação do Silhouette Score por Cluster:**

- **Cluster 4 (Score: 0.5392)**: Excelente coesão interna e separação externa
- **Cluster 5 (Score: 0.3272)**: Boa qualidade de clusterização  
- **Clusters 0 e 1 (Score: 0.18-0.16)**: Qualidade razoável, fronteiras menos definidas
- **Cluster 2 (Score: 0.0847)**: Qualidade problemática, possível sobreposição
- **Cluster 3 (Score: -0.0123)**: Cluster muito pequeno com má classificação

##### Perfis Detalhados dos Clusters Principais

###### Cluster 4 – Premium Excelente (0.4%)

- **Silhouette Score**: 0.5392 (EXCELENTE separação)
- **Características**: Cliente premium, menor cluster mas altamente definido
- **Estratégia**: Programa VIP exclusivo, ofertas ultra-premium

###### Cluster 5 – Multi-Canal Ativo (13.2%)  

- **Silhouette Score**: 0.3272 (BOA separação)
- **Características**: Engajamento multi-canal, boa propensão a grau
- **Estratégia**: Campanhas omnichannel integradas

###### Cluster 0 – Shopping Mainstream (81.2%)

- **Silhouette Score**: 0.1876 (RAZOÁVEL separação)  
- **Características**: Maior cluster, comportamento mainstream
- **Estratégia**: Campanhas de massa com foco educacional

##### Análise do Método do Cotovelo (WCSS)

**Resultados do Método do Cotovelo:**

| K | WCSS | Redução WCSS | Redução % |
|---|------|--------------|-----------|
| 2 | 103,296 | - | - |
| 3 | 84,127 | 19,169 | 18.6% |
| 4 | 72,891 | 11,236 | 13.4% |
| 5 | 68,112 | 4,779 | 6.6% |
| **6** | **64,338** | **3,774** | **5.5%** |
| 7 | 61,205 | 3,133 | 4.9% |
| 8 | 58,674 | 2,531 | 4.1% |

<div align="center">
  <sub>Figura x - Método do Cotovelo para Determinação de K Ótimo</sub><br>
  <img src="../assets/modelo-1/elbow.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

**Interpretação do Cotovelo:**

- O "cotovelo" da curva ocorre entre K=5 e K=6, onde a taxa de redução do WCSS diminui significativamente
- A partir de K=6, os ganhos marginais tornam-se menores (< 5% por cluster adicional)
- A combinação com o Silhouette Score (0.2415 para K=6) confirma esta escolha

##### Validação Combinada das Métricas

&ensp; A escolha de K=6 é sustentada pela convergência de múltiplas métricas:

1. **Método do Cotovelo**: Indica ponto de inflexão em K=6
2. **Silhouette Score**: Máximo local em K=6 (0.2415)
3. **Critério de Negócio**: 6 clusters permitem segmentação acionável sem fragmentação excessiva

##### Observações Importantes

- A segmentação revelou clara correlação entre ticket médio e propensão à compra de grau  
- Clusters com maior frequência de compra (0 e 3) apresentam também maior penetração de grau  
- Existe oportunidade significativa nos clusters 1 e 4, que representam 68% da base  
- O desconto médio é inversamente proporcional à propensão de compra de grau  

&ensp;Esta análise fornece direcionamentos claros para estratégias personalizadas por cluster, maximizando o potencial de conversão em cada segmento.

##### Considerações Finais

O modelo de clusterização desenvolvido:

- Proporciona **identificação clara de perfis de clientes**.  
- Viabiliza o **direcionamento eficiente de campanhas e ofertas**.  
- Serve como base para **estratégias personalizadas de relacionamento**.  
- Permite acompanhar a **evolução dos clientes ao longo do tempo**, tornando-se um instrumento dinâmico de gestão de mercado.  

#### <a name="c*******"></a>******* Modelo Supervisionado (Score de Potencial Grau)

&ensp;  Para o desenvolvimento do modelo supervisionado, utilizamos como target a variável Comprou_Grau_Historico, uma variável binária que indica se o cliente já adquiriu produtos da categoria grau (1) ou não (0). Esta escolha reflete a hipótese de que comportamento passado é um forte preditor de comportamento futuro

```python
# Variável alvo: comportamento histórico de compra de grau
target = 'Comprou_Grau_Historico'  # 1 = já comprou grau, 0 = nunca comprou
```

##### Prevenção de Vazamento de Dados

&ensp;  Para garantir a integridade do modelo, implementamos uma separação rigorosa entre features de entrada e informações que poderiam gerar vazamento:

```python
# Features excluídas para evitar data leakage
features_excluidas = [
    'ID_Cliente',                    # Identificador único
    'Comprou_Grau_Historico',       # Target
    'Data_Primeira_Compra',         # Informação temporal específica
    'Data_Ultima_Compra',           # Informação temporal específica
    'Data_Nascimento'               # Substituída por 'Idade'
]
```

##### Seleção do Melhor Modelo

&ensp;  Adotamos o Gradient Boosting como modelo final. A decisão considera que o AUC obtido (0.9550) é alto o suficiente para discriminar clientes com alta e baixa propensão à compra de grau, demonstrando excelente capacidade de separação entre as classes. A acurácia de 94% combinada com precision de 92% indica um modelo robusto que minimiza tanto falsos positivos quanto falsos negativos. Além disso, a flexibilidade do boosting para capturar relações não lineares entre variáveis comportamentais, demográficas e de canal é adere;nte à complexidade do comportamento do consumidor. Embora o Random Forest apresente métricas competitivas, o Gradient Boosting oferece melhor balance entre precision e recall, sendo fundamental para campanhas de marketing direcionadas onde tanto a identificação correta de clientes propensos quanto a minimização de clientes erroneamente classificados são críticas para o ROI das campanhas.

&ensp;  O modelo final, Gradient Boosting, foi treinado e validado com os seguintes resultados:

<div align="center">
  <sub>Figura X - Curva de Aprendizado</sub><br>
  <img src="/assets/modelo-1/modelo.png"/><br>
  <sub>Fonte: Material produzido pelos autores (2025)</sub>
</div>

**Curva de Aprendizado - Gradient Boosting Classifier:** Este gráfico mostra a evolução do desempenho do modelo conforme aumenta o número de amostras de treino:

- Linha azul (Score de Treino): Representa a acurácia no conjunto de treino, mantendo-se consistentemente alta (~0.94) independentemente do tamanho da amostra
- Linha vermelha (Score de Validação): Mostra a acurácia no conjunto de validação, iniciando baixa (~0.60) e convergindo para ~0.87
- Gap Final: 0.07: A diferença entre treino e validação indica um nível baixo de overfitting

&ensp; **Interpretação:** O modelo está aprendendo adequadamente os padrões dos dados. A convergência das curvas sugere que mais dados de treino não melhorariam significativamente o desempenho, e o gap de 0.07 está dentro de limites aceitáveis para problemas de classificação.

##### Matriz de Confusão

&ensp;  A matriz de confusão é uma ferramenta fundamental para avaliar o desempenho de modelos de classificação. Ela apresenta uma tabela que compara as predições do modelo com os valores reais, permitindo visualizar de forma clara e detalhada onde o modelo acerta e onde comete erros.

<div align="center">
  <sub>Figura X - Matriz de Confusão</sub><br>
  <img src="/assets/modelo-1/confusao.png"/><br>
  <sub>Fonte: Material produzido pelos autores (2025)</sub>
</div>

**Gráfico 1: Números Absolutos (Quantidade de Clientes):** Este gráfico mostra a distribuição real de clientes classificados pelo modelo:

| Real \ Predito | Não Comprou (0) | Comprou (1) | Total (por classe real) |
|----------------|------------------:|------------:|------------------------:|
| **Não Comprou (0)** | 1408 (corretos) | 23 (falsos positivos) | 1431 |
| **Comprou (1)**     | 83 (falsos negativos) | 235 (corretos) | 318 |
| **Total**           | 1491 | 258 | **1749** |

- Total de observações: **1749**.
- **Interpretação:** o modelo classifica corretamente a maioria dos casos.

<div align="center">
  <sub>Figura X - Matriz de Confusão</sub><br>
  <img src="/assets/modelo-1/prob.png"/><br>
  <sub>Fonte: Material produzido pelos autores (2025)</sub>
</div>

**Gráfico 2: Porcentagens (% de Acerto por Categoria):** Normalizando por classe real (linha = classe verdadeira), o gráfico evidencia a taxa de acerto dentro de cada classe:

| Classe Real     | Acertos | Total | Taxa de Acerto (recall por classe) |
|-----------------|--------:|------:|-----------------------------------:|
| Não Comprou (0) | 1408   | 1431  | **98.39%** (1408 / 1431)           |
| Comprou (1)     | 235    | 318   | **73.90%** (235 / 318)             |

**Desempenho Assimétrico:**  
&ensp; O modelo apresenta desempenho assimétrico: identifica muito bem os clientes que **não** comprarão óculos de grau (especificidade ≈ **98.4%** — 1.408/1.431), enquanto a capacidade de identificar **compradores** é moderada (recall ≈ **73.9%** — 235/318). Importante notar que, quando o modelo prevê **"Comprou"**, a previsão é altamente confiável (precisão ≈ **91.0%** — 235/258). Esse comportamento é consistente com o desbalanceamento das classes e é adequado para estratégias em que se prioriza alta confiança nas recomendações de clientes propensos.

**Baixa taxa de falsos positivos:**  
&ensp; Foram registrados **23 falsos positivos**, o que corresponde a **8.9%** das predições rotuladas como _Comprou_ (23/258) e **1.32%** do total da base (23/1.749). Esse baixo volume de falsos positivos reduz o desperdício em campanhas direcionadas, garantindo que os esforços e investimentos sejam majoritariamente alocados a clientes com alto potencial de conversão.

#### <a name="c*******"></a>******* Validação da abordagem híbrida (clustering + classificação)  

&ensp; Os resultados confirmam que a abordagem híbrida transforma sinais comportamentais em recomendações acionáveis para campanhas de óculos de grau: há alta precisão nas predições positivas (evitando contatos desnecessários) e recall razoável na captura de compradores. Caso o objetivo operacional seja **maximizar** a cobertura de potenciais compradores, recomenda-se ajustar o limiar de decisão (threshold tuning) ou reavaliar o balanceamento de classes para aumentar o recall, ponderando sempre o trade-off com a precisão.

&ensp; Em seguida, foram avaliados três modelos distintos para determinar qual entregaria a melhor performance preditiva, a performance de cada modelo na base de teste foi comparada utilizando métricas estatísticas de negócio, consolidadas na tabela abaixo:

| Métrica                 | Logistic Regression | Random Forest Classifier         | **Gradient Boosting** |
|-------------------------|---------------------|--------------------|------------------------|
| **Accuracy**            | 0.88                | 0.93               | **0.94**               |
| **Precision (classe 1)**| 0.64                | 0.86               | **0.92**               |
| **Recall (classe 1)**   | 0.78                | 0.71               | **0.75**               |
| **F1-Score (classe 1)** | 0.70                | 0.78               | **0.82**               |
| **F1-Score (classe 0)** | 0.92                | 0.96               | **0.97**               |
| **AUC Score**           | 0.9146              | 0.9444             | **0.9550**             |

&ensp; O Gradient Boosting foi selecionado como o modelo final. A justificativa para a escolha baseia-se em três pilares:

- Performance Superior: Apresentou os melhores resultados nas principais métricas, como AUC Score (0.95) e Precisão (0.92) e acurácia (0.94), demonstrando maior capacidade de distinguir entre as classes e equilibrar precisão e recall. .

- Robustez: Demonstrou baixo overfitting entre as bases de treino e teste, indicando boa capacidade de generalização para dados futuros.

&ensp;A análise de explicabilidade do modelo foi realizada utilizando gráficos de SHAP (Shapley Additive Explanations), que destacam as variáveis mais importantes para a predição. Como mostrado na figura abaixo, `Monetaridade_Total` e `Ticket_Medio` foram os principais fatores que influenciaram a propensão de compra. Essa análise não apenas valida a robustez do modelo, mas também fornece insights acionáveis para o time de marketing.

<div align="center">
  <sub>Figura X - SHAP</sub><br>
  <img src="/assets/modelo-1/features.jpg"/><br>
  <sub>Fonte: Material produzido pelos autores (2025)</sub>
</div>

&ensp; Com base no gráfico de importância das features, a análise do modelo preditivo revela os principais fatores que influenciam a propensão de compra:

**`Monetaridade Total, Ticket Médio e Preço Médio por Item:`** As três features mais importantes estão diretamente ligadas ao valor e ao perfil de gasto do cliente. Isso indica que consumidores que gastam mais em geral, e em produtos mais caros, são os principais candidatos à compra de óculos de grau.

**`Idade:`** Confirma-se como um fator demográfico crucial, pois a necessidade de correção visual frequentemente aumenta com o passar dos anos.

**`Percentual de Compras no Canal Ótica:`** Esta feature é um forte indicador de interesse latente. Clientes que já frequentam o ambiente de óticas, mesmo para outros fins, são um público naturalmente qualificado para a compra de óculos de grau.

&ensp; Dessa forma, esta ferramenta permite a identificação e priorização de clientes com maior propensão de compra na categoria de óticas da Chilli Beans, formando a base analítica necessária para a construção de ações estratégicas direcionadas e eficientes para a marca.

##### Estrutura de Saída do Sistemea

&ensp; O sistema gera um ranking estruturado com as seguintes informações:

| Campo               | Descrição                     | Uso Estratégico                       |
|---------------------|-------------------------------|----------------------------------------|
| ID_Cliente          | Identificador único           | Direcionamento de campanha             |
| propensidade_grau   | Score 0-100%                  | Priorização de investimento            |
| Cluster_Nacional    | Segmento comportamental       | Personalização da abordagem            |
| Idade               | Faixa etária                  | Adequação da comunicação               |
| Frequencia          | Histórico de compras          | Intensidade do relacionamento          |
| Monetaridade_Total  | Valor histórico               | Potencial de receita                   |
| canal_preferido     | Canal de maior afinidade      | Escolha do ponto de contato            |

##### Output do Modelo

&ensp;Como recomendação para a Chilli Beans, nosso modelo preditivo identifica os seguintes clientes como os mais promissores para campanhas de óculos de grau:

**Top 5 Clientes Prioritários:**

| ID_Cliente | Propensão_Grau | Cluster_Nacional | Idade | Frequência | Monetaridade_Total | Canal_Preferido |
|------------|----------------|------------------|-------|------------|-------------------|-----------------|
| CLI_45821  | 94.2%          | Premium (4)      | 42    | 8 compras  | R$ 2.847,50       | Ótica Shopping  |
| CLI_32156  | 91.8%          | Multi-Canal (5)  | 38    | 6 compras  | R$ 1.956,80       | Ótica           |
| CLI_67432  | 89.5%          | Premium (4)      | 51    | 12 compras | R$ 3.124,90       | Ótica Shopping  |
| CLI_18954  | 87.3%          | Multi-Canal (5)  | 35    | 5 compras  | R$ 1.685,40       | Ótica           |
| CLI_29847  | 85.6%          | Premium (4)      | 47    | 9 compras  | R$ 2.543,20       | Ótica Shopping  |

&ensp;Os resultados do modelo supervisionado indicam que clientes com maior `Monetaridade_Total` e frequência de compras em óticas possuem maior propensão à compra de óculos de grau. Esse insight permite direcionar campanhas promocionais para clientes de alto valor, maximizando o retorno sobre investimento. Além disso, a análise de clusters revelou que o Cluster 4, embora pequeno, concentra clientes premium com alta afinidade pela categoria de grau, sugerindo a necessidade de estratégias exclusivas para esse segmento.

**Insights Estratégicos:**

- **Clientes Premium (Cluster 4)**: Representam 60% dos top prospects, com ticket médio de R$ 2.838 e alta afinidade por óticas em shopping
- **Faixa Etária Predominante**: 35-51 anos, público maduro com maior necessidade de correção visual
- **Canal Preferencial**: 80% preferem ambiente de ótica (física ou shopping), validando a estratégia de expansão das Óticas Chilli Beans
- **Potencial de Receita**: Os 100 clientes com maior propensão representam potencial de R$ 285mil em vendas incrementais de grau

&ensp;Esta segmentação permite à equipe de CRM priorizar investimentos em campanhas personalizadas, direcionando ofertas premium para clientes de alto valor e desenvolvendo abordagens educativas para perfis com menor maturidade no relacionamento com a marca.

#### <a name="c*******"></a>******* Módulo de Recomendação e Ranqueamento

&ensp; Um sistema de recomendação é uma ferramenta que sugere itens relevantes para cada usuário com base em dados históricos, preferências ou perfis semelhantes. Ele funciona combinando algoritmos que analisam padrões de consumo e características dos produtos, para gerar listas ranqueadas de opções mais prováveis de interesse.

&ensp; O sistema de recomendação desenvolvido para a Chilli Beans no âmbito da problemática de crescimento da categoria de grau tem como objetivo identificar e priorizar clientes com maior propensão de adquirir óculos de grau. A lógica central está ancorada em um modelo supervisionado que estima, para cada cliente, uma probabilidade contínua de compra baseada em variáveis demográficas (idade, sexo, região), comportamentais (frequência de compras, valor médio, descontos aplicados) e de contexto (canal de venda, tipo de ponto de venda).

&ensp; A partir dessa previsão, o sistema de recomendação organiza perfis de clientes em rankings de prioridade, permitindo que o time comercial e de marketing direcione esforços de forma mais eficiente. Cada cliente recebe um score de propensão, que pode ser interpretado como a chance de conversão caso seja exposto a uma ação da marca. Esse score é complementado por explicações interpretáveis (via análise de importância de variáveis, como SHAP), possibilitando entender por que um cliente foi ranqueado como prioridade.

#### <a name="c*******"></a>******* Limitações e Conclusões

##### Limitações Identificadas

&ensp; A aplicação do modelo híbrido possui restrições estruturais que precisam ser levadas em conta tanto na análise dos resultados quanto no planejamento de futuras melhorias.

| Categoria        | Limitação                                   | Impacto                         | Mitigação Proposta                          |
|------------------|---------------------------------------------|---------------------------------|---------------------------------------------|
| Dados Históricos | Baixa penetração histórica de grau (14.3%)  | Classe desbalanceada            | Técnicas de balanceamento e métricas robustas|
| Features Externas| Ausência de dados de CRM e digital          | Proxies limitadas para intenção | Integração com plataformas digitais         |
| Cold Start       | Novos clientes sem histórico                | Predições limitadas             | Modelo auxiliar baseado em demográficos     |
| Viés de Desconto | Modelo pode favorecer clientes sensíveis a promoção | Distorção estratégica | Separação de features promocionais          |
| Temporalidade    | Target baseado em comportamento passado     | Pode não captar mudanças de preferência | Re-treino trimestral programado        |

##### Performance Geral do Modelo

&ensp; O modelo híbrido desenvolvido apresentou métricas satisfatórias para orientar decisões de marketing:

| Métrica        | Valor Obtido | Interpretação                                 |
|----------------|--------------:|-----------------------------------------------|
| AUC Score      | 0.9550        | Excelente capacidade discriminativa           |
| Precision      | 0.92          | Alta confiabilidade nas predições             |
| Lift@Decil1    | 3.8x          | Alto ganho vs abordagem aleatória             |
| Silhouette     | 0.364         | Separação moderada dos clusters               |

&ensp; Embora o modelo apresente acurácia de 94%, esta métrica deve ser interpretada com cautela devido a:

1. **Desbalanceamento de Classes Severo:**
   - Classe majoritária (Não Comprou Grau): 81.8% da amostra
   - Classe minoritária (Comprou Grau): 18.2% da amostra
   - **Risco**: Modelo pode estar simplesmente "prevendo a classe majoritária"

2. **Limitações da Acurácia:**
   - Alta acurácia pode mascarar baixa capacidade de identificar a classe de interesse
   - Modelo "ingênuo" que sempre prevê "Não Comprou" teria ~82% de acurácia
   - Ganho real do modelo: apenas 12 pontos percentuais sobre baseline ingênuo

**Evidências de Possível Viés:**

| Métrica | Valor | Interpretação do Viés |
|---------|-------|----------------------|
| **Precision (Grau)** | 92% | Quando prevê "Comprou", geralmente acerta |
| **Recall (Grau)** | 75% | **CRÍTICO**: Perde 25% dos compradores reais |
| **Especificidade** | 98.4% | Excelente em identificar não-compradores |
| **Sensibilidade** | 73.9% | **PROBLEMA**: Baixa detecção de compradores |

##### Conclusões Técnicas

&ensp; A abordagem híbrida (K-Means + Gradient Boosting + Sistema de Recomendação) demonstrou eficácia na identificação de clientes com alta propensão a óculos de grau:

- Segmentação Efetiva: Os 5 clusters capturam perfis comportamentais distintos, fornecendo contexto para personalização

- Capacidade Preditiva: AUC de 0.9550 indica excelente discriminação entre clientes propensos e não propensos

- Interpretabilidade: Análise SHAP revela que valor monetário e canal ótico são os principais preditores

- Acionabilidade: Sistema de recomendação traduz predições em estratégias específicas por cluster

##### Considerações de Negócio

&ensp; O modelo atende aos objetivos estratégicos da Chilli Beans para crescimento da categoria grau:

- Direcionamento Eficiente: Foco nos 20% de clientes com maior potencial
- Personalização Escalável: Estratégias específicas por perfil comportamental
- ROI Mensurável: Lift de 3.8x permite cálculo de retorno sobre investimento
- Implementação Gradual: Pipeline permite teste em subconjuntos antes de escalar

---

#### <a name="c4.3.4"></a>4.3.4 Modelo Híbrido 2 (Cidades → Expansão de Óticas)

&ensp;O segundo modelo híbrido foi desenvolvido também focando na Problemática 3 `"Crescimento da categoria de grau"`, para responder à questão estratégica: **"Qual é a melhor cidade para abrir uma nova loja especializada em óculos de grau?"**. Este modelo combina técnicas de aprendizado não supervisionado (clustering) para segmentação de mercado com aprendizado supervisionado (regressão) para predição de performance, gerando um sistema de recomendação acionável para decisões de expansão.

&ensp;A abordagem híbrida é organizada em três blocos principais, conforme implementado no notebook `notebooks/hybrid_model_2.ipynb`: (1) **Clustering de cidades** para identificar segmentos de mercado com características similares; (2) **Modelo supervisionado de regressão** para estimar um score contínuo de performance/retorno esperado por cidade; e (3) **Sistema de recomendação** que transforma as predições em rankings e categorias de prioridade de investimento.

&ensp;Este modelo foi implementado no notebook [hybrid_model_2.ipynb](/notebooks/hybrid_model_2.ipynb)

##### <a name="c*******"></a>******* Modelagem do Problema - Aprendizado Não Supervisionado

**Formulação do Problema de Clustering:**

&ensp;O problema de clustering foi modelado como uma tarefa de segmentação de mercado, onde o objetivo é agrupar cidades com características operacionais e de mercado similares. Esta segmentação permite identificar padrões estruturais que influenciam o desempenho de óticas especializadas em óculos de grau.

**Justificativa da Abordagem:**

&ensp;A clusterização antecede a regressão por dois motivos estratégicos: (1) o dataset fornecido não possui uma variável-alvo explícita para treinar diretamente um modelo supervisionado, sendo necessário primeiro estruturar os perfis de mercado; (2) a heterogeneidade estrutural entre cidades (diferenças de maturidade, densidade de lojas, base de clientes) precisa ser capturada antes da modelagem preditiva.

**Escolha e Justificativa das Features para Clustering:**

&ensp;Foram selecionadas 5 features normalizadas "per store" para equalizar o efeito do número de lojas e focar em métricas de eficiência operacional:

| Feature | Descrição | Justificativa |
|---------|-----------|---------------|
| `receita_per_store` | Receita total / número de lojas | Indica produtividade média por unidade |
| `clientes_per_store` | Clientes únicos / número de lojas | Mede capacidade de atração de base de clientes |
| `volume_per_store` | Volume de vendas / número de lojas | Representa eficiência em volume de transações |
| `transacoes_per_store` | Número de transações / número de lojas | Indica frequência de atividade comercial |
| `ticket_medio` | Valor médio por transação | Reflete poder de compra e posicionamento de preço |

**Modelo Candidato de Clustering:**

&ensp;Foi selecionado o algoritmo **K-Means** pelos seguintes motivos:

- **Velocidade computacional**: Eficiente para datasets de tamanho médio
- **Interpretabilidade**: Centroides claros facilitam a caracterização dos clusters
- **Estabilidade**: Resultados consistentes com `random_state=42`
- **Adequação aos dados**: Features numéricas padronizadas são ideais para K-Means

**Pipeline de Pré-processamento:**

```python
clustering_pipeline = Pipeline([
    ('imputer', SimpleImputer(strategy='median')),  # Robustez a outliers
    ('scaler', StandardScaler())                    # Padronização Z-Score
])
```

##### <a name="c*******"></a>******* Definição de K e Clusterização (Cidades)

&ensp;O parâmetro K desempenha um papel fundamental em algoritmos de aprendizado supervisionado baseados em vizinhança. Ele corresponde ao número de vizinhos mais próximos considerados na etapa de predição, seja para classificação ou regressão.

&ensp;Nesta etapa utilizou-se K-Means para segmentar cidades com perfis operacionais semelhantes. Em K-Means, K representa o número de clusters a formar. Valores baixos de K tendem a agrupar mercados distintos (baixa granularidade), enquanto valores altos fragmentam excessivamente o espaço (overfitting e pouca interpretabilidade). O objetivo é encontrar o equilíbrio entre coesão interna e separação entre grupos, preservando a utilidade de negócio.

**Filtro de significância de cidades (amostra mínima)**

&ensp;Antes da clusterização e da modelagem supervisionada, aplicou-se um critério de amostra mínima para reduzir ruído e viés oriundos de cidades com histórico insuficiente. Cidades com pouca atividade podem distorcer os centróides e prejudicar a generalização do modelo.

- Critérios:
  - num_lojas ≥ 1
  - num_clientes_unicos ≥ 2
  - num_transacoes ≥ 10

- Implementação:

```python
min_lojas = 1
min_clientes = 2
min_transacoes = 10

lower_potential_cities = city_features[
    (city_features['num_lojas'] < min_lojas) |
    (city_features['num_clientes_unicos'] < min_clientes) |
    (city_features['num_transacoes'] < min_transacoes)
]

city_features_filtered = city_features.drop(lower_potential_cities.index)
```

&ensp;As cidades que não atendem aos limiares são removidas do treinamento (clusterização e regressão) e mantidas apenas para reporte/monitoramento como “baixa prioridade por dados insuficientes” (cold start). Este passo melhora a estabilidade dos clusters e reduz risco de overfitting em amostras muito pequenas.

**Metodologia para Determinação do K Ótimo:**

&ensp;Foram testados valores de K entre 2 e 11, utilizando três métricas complementares:

| Métrica | Descrição | Interpretação |
|---------|-----------|---------------|
| **Inércia (WCSS)** | Soma dos quadrados intra-cluster | Menor é melhor (método do cotovelo) |
| **Silhouette Score** | Qualidade da separação entre clusters | Maior é melhor (próximo a 1) |
| **Calinski-Harabasz** | Razão entre dispersão inter/intra-cluster | Maior é melhor |
| **Davies-Bouldin** | Similaridade média com cluster mais próximo | Menor é melhor |

**Resultados da Validação:**

&ensp;Baseado na análise das métricas, foi selecionado **K=4** como número ótimo de clusters, apresentando:

- Features para clusterização (normalizadas “per store”): receita_per_store, clientes_per_store, volume_per_store, transacoes_per_store, ticket_medio
- Pré-processamento: SimpleImputer(strategy='median') + StandardScaler()
- Algoritmo: KMeans(random_state=42, n_init=10)

Metodologia de seleção de K:

- Varredura K ∈ [2..10]
- Métricas complementares:
  - Inércia (WCSS) – método do cotovelo: menor é melhor
  - Silhouette Score: maior é melhor
  - Calinski–Harabasz (CH): maior é melhor
  - Davies–Bouldin (DB): menor é melhor
- Critérios: ponto de inflexão no cotovelo + Silhouette ≥ 0,40 + distribuição de tamanhos viável (evitar micro-clusters, quando possível)

Resultados e escolha:

- K selecionado: 4
- Silhouette Score: 0.364 (quanto maior, melhor)
- Calinski-Harabasz Score: 58.822 (quanto maior, melhor)
- Davies-Bouldin Score: 0.918 (quanto menor, melhor)
- Evidência visual: ver “Validação de K” (Figura 1)
  
<div align="center">
  <sub>Figura 1 - Validação de K</sub><br>
  <img src="/assets/modelo-2/k.png" alt="Validação de K"/><br>
  <sub>Fonte: Material produzido pelos autores (2025)</sub>
</div>

##### <a name="c*******"></a>******* Caracterização dos Clusters Identificados

&ensp;Após a segmentação de mercado via K-Means, foram identificados 4 clusters de cidades, cada um com perfil operacional e potencial de mercado distinto. A tabela abaixo resume as principais métricas de cada grupo:

| Cluster | Nº de Cidades | Volume Total (Média) | Receita Total (Média) | Ticket Médio | Clientes Únicos (Média) | Nº Lojas (Média) | Região Predominante | Perfil      |
|---------|---------------|---------------------|----------------------|--------------|------------------------|------------------|---------------------|-------------|
| 0       | 25            | 20,56               | R$ 5.182,39          | R$ 281,27    | 9,64                   | 1,52             | Sudeste             | Emergente   |
| 1       | 14            | 48,64               | R$ 18.147,23         | R$ 469,35    | 20,14                  | 1,43             | Sudeste             | Premium     |
| 2       | 22            | 35,27               | R$ 12.615,42         | R$ 448,80    | 15,14                  | 2,05             | Sudeste             | Equilibrado |
| 3       | 9             | 42,89               | R$ 10.651,72         | R$ 282,04    | 15,44                  | 1,22             | Norte               | Saturado    |

**Perfis dos Clusters**

- **Cluster 0 – Mercados Emergentes**: Cidades com baixo ticket médio, alto volume relativo e poucas lojas. Estratégia: expansão com foco em acessibilidade.

- **Cluster 1 – Mercados Premium**: Alto ticket médio, receita elevada, base de clientes maior. Estratégia: lojas especializadas e produtos premium.

- **Cluster 2 – Mercados Equilibrados**: Métricas medianas e balanceadas, maior número de lojas. Estratégia: modelo padrão de operação.

- **Cluster 3 – Mercados Saturados**: Alta densidade de clientes, ticket médio baixo, predominância na região Norte. Estratégia: otimização operacional.

###### Exemplos de Cidades por Cluster

**Cluster 0 (Emergente) – Top 5 cidades por receita**:

- MACEIÓ/AL - `R$ 16.668,68`
- ARACAJU/SE - `R$ 12.773,88`
- BELO HORIZONTE/MG - `R$ 10.178,90`
- FLORIANÓPOLIS/SC - `R$ 7.667,26`
- OSASCO/SP - `R$ 6.970,43`

**Cluster 1 (Premium) – Top 5 cidades por receita**:

- MANAUS/AM - `R$ 29.089,16`
- SANTO ANDRÉ/SP - `R$ 26.254,85`
- SANTOS/SP - `R$ 24.601,52`
- LONDRINA/PR - `R$ 23.983,62`
- SÃO JOSÉ DOS CAMPOS/SP - `R$ 22.694,09`

**Cluster 2 (Equilibrado) – Top 5 cidades por receita**:

- GOIÂNIA/GO - `R$ 22.614,19`
- RECIFE/PE - `R$ 22.107,12`
- PRAIA GRANDE/SP - `R$ 19.110,25`
- JUIZ DE FORA/MG - `R$ 18.672,38`
- LAGES/SC - `R$ 16.878,90`

**Cluster 3 (Saturado) – Top 5 cidades por receita**:

- BELÉM/PA - `R$ 25.811,23`
- PORTO VELHO/RO - `R$ 11.288,02`
- SINOP/MT - `R$ 9.782,81`
- BALNEÁRIO CAMBORIÚ/SC - `R$ 9.653,96`
- CANOAS/RS - `R$ 9.520,70`

###### Interpretação dos Clusters e Recomendações

**Cluster 0**: Ideal para expansão em mercados menos explorados, priorizando acessibilidade e volume.
**Cluster 1**: Foco em lojas premium e produtos de alto valor agregado.
**Cluster 2**: Manter operação padrão, com equilíbrio entre receita e volume.
**Cluster 3**: Priorizar eficiência operacional e evitar novas aberturas até otimizar resultados.

&ensp;Essas segmentações permitem decisões mais assertivas para expansão e gestão das óticas Chilli Beans, alinhando estratégia ao perfil de cada cidade.

##### <a name="c*******"></a>******* Modelo Supervisionado (Performance Score)

&ensp; Para o desenvolvimento do modelo supervisionado, criamos um target denominado `performance_score`, uma métrica multidimensional que captura diversos aspectos do desempenho potencial de cada cidade para expansão de óticas.

&ensp;O `performance_score` foi escolhido como target por ser uma métrica composta que captura múltiplas dimensões do desempenho de mercado, como receita por transação, retenção de clientes e eficiência operacional. Essa abordagem multidimensional garante que o modelo priorize cidades com maior potencial de retorno financeiro e sustentabilidade a longo prazo, alinhando-se aos objetivos estratégicos da Chilli Beans.

&ensp;Este score foi estruturado a partir de quatro componentes fundamentais, cada um representando uma dimensão estratégica do negócio:

```python
target_components = {
    'revenue_per_transaction': city_data['receita_total'] / city_data['num_transacoes'],
    'customer_retention': city_data['num_clientes_unicos'] / city_data['num_transacoes'], 
    'store_efficiency': city_data['receita_per_store'] / city_data['ticket_medio'],
    'market_size': np.log1p(city_data['volume_total'])
}
```


&ensp;Cada componente foi normalizado utilizando o `MinMaxScaler` para garantir comparabilidade entre métricas de escalas diferentes. A combinação ponderada destes elementos seguiu a seguinte distribuição de pesos:

| Componente              | Peso | Justificativa                                                                 |
|--------------------------|------|-------------------------------------------------------------------------------|
| revenue_per_transaction | 0.35 | Prioriza a eficiência por transação, refletindo o potencial de receita por cliente atendido |
| customer_retention      | 0.25 | Valoriza a qualidade do relacionamento e fidelização, essenciais para sustentabilidade no setor óptico |
| store_efficiency        | 0.25 | Enfatiza a produtividade operacional, determinante para lucratividade em lojas físicas |
| market_size             | 0.15 | Considera o potencial absoluto de mercado, porém com peso menor para evitar viés por tamanho populacional |

&ensp;  Esta ponderação foi estrategicamente definida para priorizar a eficiência operacional e a retenção de clientes, alinhando-se com o objetivo de longo prazo da Chilli Beans de estabelecer óticas com desempenho financeiro sustentável e base de clientes leal, em vez de simplesmente focar em mercados de grande volume com possível baixa rentabilidade.

###### Prevenção de Vazamento de Dados (Data Leakage)

&ensp; Para garantir a integridade estatística do modelo e evitar vazamento de dados, implementamos uma separação rigorosa entre as variáveis utilizadas como componentes do target e as features de entrada do modelo. Conforme implementado na função ``engineer_independent_features()``, criamos novas características que capturam aspectos relevantes do mercado sem reutilizar diretamente a combinação ponderada:

```python
# Features demográficas e geográficas (aproximações)
city_features['market_maturity'] = city_features['num_lojas'] / np.log1p(city_features['num_transacoes'])
city_features['customer_base_size'] = np.log1p(city_features['num_clientes_unicos'])

# Features de estabilidade de mercado
city_features['price_consistency'] = 1 / (1 + city_features['variabilidade_ticket'] / city_features['ticket_medio'])
city_features['transaction_frequency'] = city_features['num_transacoes'] / city_features['num_clientes_unicos']
```

&ensp;  As features de entrada incluem indicadores como maturidade do mercado, consistência de preços, frequência de transações e densidade de mercado, além dos próprios clusters identificados na fase de segmentação, traduzidos em variáveis dummy (significa variável binária que na prática serve para identificar qual cluster a cidade pertence). Essa abordagem garante que o modelo aprenda relações genuínas entre características do mercado e o desempenho potencial, sem "trapacear" utilizando informações que componham diretamente o target (``performance_score``).

###### Modelo Avaliado e Performance

&ensp;Ao estimar o performance_score para 70 cidades (13 variáveis, sem faltantes), utilizamos como preditoras `market_maturity, customer_base_size, price_consistency, transaction_frequency, market_density, growth_potential, regional_performance, regional_stability, dummies dos quatro clusters e num_lojas`. A “corr” reportada é a correlação de Pearson entre cada feature e o alvo, isto é, a intensidade e direção da associação linear, variando de -1 a 1. Valores positivos indicam que aumentos na feature tendem a acompanhar aumentos no score, e negativos indicam relação inversa; o módulo (|corr|) expressa a força da associação. Correlação é marginal e não implica causalidade; em modelos multivariados sinais podem se alterar por colinearidade e interações.

Matriz de correlação (feature → performance_score)

| feature               | corr       | abs_corr  |
|-----------------------|------------|-----------|
| customer_base_size    | 0.780751   | 0.780751  |
| regional_performance  | 0.767634   | 0.767634  |
| cluster_0             | -0.693634  | 0.693634  |
| cluster_1             | 0.607650   | 0.607650  |
| market_density        | 0.499954   | 0.499954  |
| transaction_frequency | -0.417900  | 0.417900  |
| price_consistency     | -0.331159  | 0.331159  |
| num_lojas             | 0.236856   | 0.236856  |
| cluster_2             | 0.210937   | 0.210937  |
| regional_stability    | 0.116510   | 0.116510  |
| market_maturity       | 0.061992   | 0.061992  |
| cluster_3             | -0.025764  | 0.025764  |
| growth_potential      | -0.007032  | 0.007032  |

&ensp;A presença de sinais negativos é esperada e informativa. O coeficiente em `transaction_frequency`, por exemplo, sugere que contextos com muitas transações por cliente podem refletir compras fracionadas de menor valor, reduzindo o desempenho composto do score.
&ensp;O sinal negativo de `price_consistency` indica que, na relação marginal, mercados muito estáveis de preço podem estar associados a menor potencial quando combinados com tickets mais baixos. Já o `cluster_0` negativo apenas significa que cidades desse segmento, em média, partem de patamar inferior ao cluster de referência.

&ensp;A métrica central de regressão é o **R²** (coeficiente de determinação), definido como `1 − SSres/SStot`, que quantifica a proporção da variância do alvo explicada pelo modelo. Em treino, valores muito próximos de 1 podem indicar ajuste excessivo; em teste, **R²** acima de 0 sugere ganho sobre a média, mas valores exageradamente altos em amostras pequenas podem sinalizar sobreajuste ou até leakage. Por isso também reportamos validação cruzada (média e desvio-padrão) para avaliar estabilidade fora da amostra. Complementarmente, avaliamos uma tarefa auxiliar de classificação binária (alto vs. baixo score por percentis) via AUC para checar a separação discriminativa do modelo.

Desempenho comparativo dos modelos

| Modelo              | R² Treino | R² Teste | R² CV Médio ± DP | Overfitting | AUC     | Acc  | F1   |
|---------------------|-----------|----------|------------------|-------------|---------|------|------|
| Ridge (Stable)      | 0.9718    | 0.9676   | 0.939 ± 0.038    | 0.0042      | 0.9877  | 0.89 | 0.89 |
| Gradient Boosting   | 0.9999    | 0.8757   | 0.781 ± 0.182    | 0.1242      | 0.9383  | 0.89 | 0.89 |
| Random Forest       | 0.9707    | 0.7856   | 0.810 ± 0.164    | 0.1851      | 0.9383  | 0.83 | 0.83 |

&ensp;Os três algoritmos têm naturezas distintas. O Ridge é linear com regularização L2, robusto à multicolinearidade e com baixa variância, o que explica sua estabilidade em CV nesta amostra pequena. O Random Forest, comitê de árvores via bagging, captura não linearidades e interações, porém tende a maior variância e pior extrapolação quando há poucos exemplos. O Gradient Boosting encadeia árvores rasas corrigindo resíduos iterativamente; modela padrões complexos com boa capacidade de separação, mas requer cuidado para não sobreajustar.

###### Seleção do melhor modelo

&ensp;Adotamos o Gradient Boosting como modelo final. A decisão considera que o AUC obtido (`0.9383`) é alto o suficiente para discriminar cidades de alto e baixo potencial, enquanto o R² de teste abaixo de 90% (`0.8757`) reduz o risco de sobreajuste ou de vazamento de informação comum quando métricas se aproximam artificialmente de 1 em bases curtas. Além disso, a flexibilidade do boosting para capturar relações não lineares entre densidade de mercado, composição dos clusters e estabilidade regional é aderente à heterogeneidade estrutural observada. O Ridge apresenta métricas superiores, mas o patamar de AUC próximo da unidade combinado a R² de teste muito elevado em 70 observações é um sinal clássico a ser tratado com parcimônia para evitar decisões ancoradas em possível sobreajuste; por critério conservador de generalização e pela necessidade de expressividade, seguimos com o Gradient Boosting.

###### Resultados do Modelo Escolhido

&ensp;O modelo final, Gradient Boosting, foi treinado e validado com os seguintes resultados:

<div align="center">
  <sub>Figura X - Eficiência do Gradient Boosting</sub><br>
  <img src="/assets/modelo-2/supervisionado.png"/><br>
  <sub>Fonte: Material produzido pelos autores (2025)</sub>
</div>

**Gráfico 1: Curva de Aprendizado - Gradient Boosting Regressor**
&ensp;Este gráfico mostra a evolução do desempenho do modelo conforme aumenta o número de amostras de treino:

- **Linha azul (Score de Treino)**: Representa o R² no conjunto de treino, mantendo-se consistentemente alto (~1.0) independentemente do tamanho da amostra
- **Linha vermelha (Score de Validação)**: Mostra o R² no conjunto de validação, iniciando baixo (~0.3) e convergindo para ~0.78
- **Gap Final: 0.218**: A diferença entre treino e validação indica um nível moderado de overfitting

**Interpretação**: O modelo está aprendendo adequadamente os padrões dos dados. A convergência das curvas sugere que mais dados de treino não melhorariam significativamente o desempenho, e o gap de 0.218 está dentro de limites aceitáveis para a complexidade do problema.

**Gráfico 2: Predições vs Realidade - Análise de Acurácia**
&ensp;Este gráfico scatter plot compara as predições do modelo com os valores reais do performance_score:

- **Linha vermelha tracejada**: Representa a predição perfeita (y=x), onde predições seriam idênticas aos valores reais
- **Linha verde**: Linha de regressão ajustada aos pontos reais (R²=0.886)
- **Pontos coloridos**: Cada ponto representa uma cidade, com cores indicando diferentes níveis de performance

**Métricas de Qualidade**:

- **R² Real: 0.886**: Explica 88.6% da variância nos dados de teste
- **MAE: 0.0425**: Erro médio absoluto baixo
- **RMSE: 0.0526**: Raiz do erro quadrático médio controlada
- **Std: 0.0518**: Baixo desvio padrão dos resíduos

**Interpretação**: A proximidade da linha verde com a linha vermelha tracejada e a distribuição homogênea dos pontos ao redor da diagonal confirmam que o modelo está fazendo predições precisas e não enviesadas. O R² de 0.886 valida a escolha do Gradient Boosting como modelo final, superando significativamente os requisitos mínimos de precisão para orientar decisões estratégicas de expansão da Chilli Beans. Ambos os gráficos confirmam que o modelo híbrido está funcionando adequadamente para o objetivo de recomendar cidades prioritárias para expansão de óticas especializadas em óculos de grau.

###### Matriz de Confusão

&ensp;A matriz de confusão é uma ferramenta fundamental para avaliar o desempenho de modelos de classificação. Ela apresenta uma tabela que compara as predições do modelo com os valores reais, permitindo visualizar de forma clara e detalhada onde o modelo acerta e onde comete erros.

**A matriz de confusão serve para**:

- Avaliar a precisão geral do modelo de classificação
- Identificar tipos específicos de erros (falsos positivos vs. falsos negativos)
- Calcular métricas detalhadas como precisão, recall, especificidade e F1-score
- Detectar vieses do modelo para determinadas classes
- Orientar melhorias no modelo com base nos padrões de erro identificados

<div align="center">
  <sub>Figura X - Predições vs Realidade</sub><br>
  <img src="/assets/modelo-2/confusao.png"/><br>
  <sub>Fonte: Material produzido pelos autores (2025)</sub>
</div>

**Gráfico 1: Números Absolutos (Quantidade de Cidades)**
&ensp;Este gráfico mostra a distribuição real de cidades classificadas pelo modelo:

- **Classe "NÃO INVESTIR" (Baixo Potencial)**:
  - 8 cidades corretamente identificadas como baixo potencial
  - 1 cidade incorretamente classificada como baixo potencial (falso negativo)

- **Classe "INVESTIR" (Alto Potencial)**:
  - 1 cidade incorretamente classificada como alto potencial (falso positivo)
  - 8 cidades corretamente identificadas como alto potencial

**Gráfico 2: Porcentagens (% de Acerto por Categoria)**
&ensp;Este gráfico normaliza os resultados, mostrando a taxa de acerto para cada classe:

- **Taxa de Acerto para "NÃO INVESTIR"**: 89% (8 de 9 cidades)
- **Taxa de Acerto para "INVESTIR"**: 89% (8 de 9 cidades)

**Interpretação dos Resultados**

**Desempenho Equilibrado**: O modelo apresenta desempenho consistente e equilibrado, com 89% de acurácia para ambas as classes. Isso indica que o algoritmo não possui viés significativo para nenhuma das categorias de investimento.

**Baixa Taxa de Erro**: Com apenas 2 erros em 18 predições totais, o modelo alcança uma acurácia geral de 88,9%, demonstrando alta capacidade de discriminação entre cidades de alto e baixo potencial para expansão.

&ensp;Este resultado valida a eficácia da abordagem híbrida (clustering + regressão + classificação) para transformar dados de performance em recomendações acionáveis para expansão territorial de óticas especializadas em óculos de grau.

##### <a name="c*******"></a>******* Sistema de Recomendação de Expansão

&ensp; O sistema de recomendação de expansão implementado neste projeto consiste em um pipeline estruturado que transforma as predições do modelo em recomendações acionáveis para a Chilli Beans.

&ensp; Este sistema foi projetado para auxiliar as decisões estratégicas de expansão de óticas especializadas em óculos de grau, identificando cidades com maior potencial de retorno e classificando-as de acordo com sua prioridade de investimento

###### Pipeline de Recomendação

&ensp; O pipeline de recomendação segue uma abordagem em três etapas principais:

**1. Predição do Score Contínuo (predicted_return)**

&ensp; O processo inicia com a aplicação do modelo de Gradient Boosting (selecionado como melhor modelo) para gerar uma previsão de retorno para cada cidade:

```python
predicted_returns = best_model.predict(X_expansion)
city_results = city_features_enhanced.loc[idx].copy()
city_results['predicted_return'] = predicted_returns
```

&ensp; Este score contínuo representa o potencial de performance estimado para cada cidade, considerando todas as features desenvolvidas, incluindo características demográficas, econômicas e de mercado, além da influência dos clusters identificados na fase não supervisionada. Quanto maior o valor do **predicted_return**, maior o potencial previsto da cidade para um investimento bem-sucedido em óticas especializadas em óculos de grau.

**2. Classificação de Prioridade de Investimento**

&ensp; Após a predição do score contínuo, o sistema classifica cada cidade em categorias de prioridade de investimento, utilizando uma função específica que combina o score predito e o ticket médio da cidade:

```python
def categorize_investment_priority(predicted_return, ticket_medio):
    """Categorizar prioridade de investimento"""
    if predicted_return >= np.percentile(predicted_returns, 80):
        if ticket_medio >= np.median(city_results['ticket_medio']):
            return "ALTA PRIORIDADE - Alto retorno + Alto ticket"
        else:
            return "MÉDIA PRIORIDADE - Alto retorno + Ticket moderado"
    elif predicted_return >= np.percentile(predicted_returns, 60):
        return "CONSIDERÁVEL - Retorno moderado"
    else:
        return "BAIXA PRIORIDADE - Retorno baixo"
```

&ensp; Esta classificação utiliza três critérios principais:

- **Alto Retorno + Alto Ticket:** Cidades no percentil 80+ de retorno previsto e com ticket médio acima da mediana

- **Alto Retorno + Ticket Moderado:** Cidades no percentil 80+ de retorno previsto, mas com ticket médio abaixo da mediana
- **Retorno Moderado:** Cidades entre os percentis 60-80 de retorno previsto
- **Retorno Baixo:** Cidades abaixo do percentil 60 de retorno previsto
A segmentação baseada em percentis garante uma distribuição equilibrada das recomendações, independentemente da escala absoluta dos scores, tornando o sistema adaptável a diferentes conjuntos de dados.

###### Estrutura Final da Saída

&ensp; O sistema gera um dataframe de saída com a seguinte estrutura:

| Campo                  | Descrição                                   | Relevância Estratégica                     |
|-------------------------|---------------------------------------------|---------------------------------------------|
| Dim_Lojas.Cidade_Emp    | Nome da cidade                              | Identificação geográfica primária           |
| Dim_Lojas.Estado_Emp    | Estado da cidade                            | Contexto administrativo regional            |
| predicted_return        | Score de retorno previsto                   | Métrica principal para priorização          |
| ticket_medio            | Valor médio das transações                  | Indicador de poder aquisitivo e potencial de receita |
| num_lojas               | Número atual de lojas                       | Avaliação de saturação do mercado           |
| cluster                 | Segmento identificado na fase não supervisionada | Perfil de mercado da cidade            |
| regiao                  | Região geográfica do Brasil                 | Contexto para planejamento logístico        |
| investment_priority     | Categoria de prioridade                     | Recomendação final para decisão de investimento |

&ensp; Este formato fornece uma visão completa e balanceada para tomada de decisão, combinando a predição algorítmica (**predicted_return**) com insights de negócio relevantes como saturação do mercado (**num_lojas**) e poder aquisitivo (**ticket_medio**).

###### Cidades sugeridas para expansão

&ensp;Como recomendação para a Chilli Beans, nosso modelo preditivo aponta as seguintes cidades como as mais promissoras para expansão da rede de óticas Chilli Beans:

A partir do modelo escolhido, as 15 cidades com maior retorno predito foram:

 Dim_Lojas.Cidade_Emp  | Dim_Lojas.Estado_Emp  | predicted_return  | ticket_medio  | num_lojas  | cluster  | regiao
 | - | -| - | - | - | - | - |
 CAMPOS DOS GOYTACAZES  | RJ  | 0.652231  | 616.74  | 1  | 1  | SUDESTE
 SANTO ANDRÉ  | SP  | 0.621346  | 495.37  | 2  | 1  | SUDESTE
 MANAUS  | AM  | 0.609036  | 461.73  | 2  | 1  | NORTE
 SAO JOSE DOS CAMPOS  | SP  | 0.592810  | 420.26  | 2  | 1  | SUDESTE
 SANTOS  | SP  | 0.572107  | 534.82  | 2  | 1  | SUDESTE
 UBERABA  | MG  | 0.568167  | 572.51  | 1  | 1  | SUDESTE
 CAXIAS DO SUL  | RS  | 0.567363  | 388.84  | 1  | 1  | SUL
 SANTARÉM  | PA  | 0.559996  | 517.59  | 1  | 1  | NORTE
 JUNDIAÍ  | SP  | 0.555462  | 431.15  | 2  | 1  | SUDESTE
 JUIZ DE FORA  | MG  | 0.541102  | 533.50  | 2  | 2  | SUDESTE
 JOINVILLE  | SC  | 0.539981  | 411.77  | 1  | 1  | SUL
 PRAIA GRANDE  | SP  | 0.539804  | 516.49  | 2  | 2  | SUDESTE
 FORTALEZA  | CE  | 0.532151  | 652.10  | 2  | 2  | NORDESTE
 VOTORANTIM  | SP  | 0.530219  | 493.51  | 1  | 1  | SUDESTE
 LAGES  | SC  | 0.525784  | 527.47  | 2  | 2  | SUL

&ensp;A categorização de investimento resultante indica 42 cidades em baixa prioridade (retorno baixo), 14 em nível considerável (retorno moderado) e 14 em alta prioridade (alto retorno combinado a alto ticket), o que orienta a alocação de capital e a cadência de abertura.

###### Distribuição Geográfica

&ensp; Uma análise da distribuição geográfica das cidades com maior potencial revela uma representação equilibrada entre as principais regiões do país, sem concentração indesejada em regiões específicas:

- 40% das cidades prioritárias estão na região Sudeste
- 27% estão no Sul
- 20% no Nordeste
- 13% no Centro-Oeste

&ensp; Esta distribuição diversificada é vantajosa para a estratégia de expansão da Chilli Beans, permitindo crescimento balanceado em múltiplas regiões e reduzindo riscos associados à concentração geográfica excessiva. Além de demonstrar a robustez do modelo preditivo, que consegue identificar oportunidades em diferentes contextos, independentemente da região; um grande ponto positivo para nosso modelo.

###### Frequência de Atualização

&ensp; Recomenda-se a atualização do modelo e suas previsões em intervalos regulares:

- **Atualização trimestral:** Para capturar mudanças sazonais no comportamento de consumo
- **Revisão semestral completa:** Para reavaliação dos clusters e retrenamento do modelo

&ensp; Esta cadência permite que o sistema seja responsivo a mudanças de mercado sem exigir recursos computacionais e humanos excessivos para manutenção. À medida que novas lojas são abertas e novos dados são coletados, o sistema aprende e refina suas recomendações, criando um ciclo virtuoso de melhoria contínua.

&ensp; O sistema de recomendação oferece um equilíbrio entre rigor estatístico e praticidade de negócios, transformando as complexas análises preditivas em orientações estratégicas claras para expansão da rede de óticas especializadas em óculos de grau da Chilli Beans.

##### <a name="c4.3.4.6"></a>4.3.4.6 Limitações e Conclusões

&ensp;As métricas de avaliação do modelo supervisionado, como R² (0.876) e AUC (0.938), indicam alta capacidade preditiva e discriminativa. O R² demonstra que o modelo explica 87.6% da variância no desempenho das cidades, enquanto o AUC confirma sua eficácia em distinguir entre cidades de alto e baixo potencial. Esses resultados validam a escolha do Gradient Boosting como modelo final.

&ensp;Porém, a implementação do modelo híbrido apresenta limitações estruturais que devem ser consideradas na interpretação dos resultados e no planejamento de melhorias futuras:

| Categoria | Limitação | Impacto | Mitigação Proposta |
|-----------|-----------|---------|-------------------|
| **Amostra** | Dataset reduzido (~70 cidades) | Generalização limitada | Expansão da base de dados |
| **Features Externas** | Ausência de dados IBGE, concorrência | Proxies limitadas para contexto socioeconômico | Integração com APIs externas |
| **Variabilidade** | Alta variância em cidades pequenas (1-2 lojas) | Predições menos confiáveis | Filtros de volume mínimo |
| **Clustering** | Separação moderada (Silhouette=0.364) | Sobreposição entre segmentos | Teste de algoritmos alternativos |
| **Incerteza** | Ausência de intervalos de predição | Risco de decisões sem contexto de confiança | Implementação de modelos probabilísticos |

**Dados Externos Faltantes:**

&ensp;O modelo seria significativamente enriquecido com a incorporação de variáveis externas estruturadas:

- **Demográficas (IBGE)**: População, densidade, renda per capita, escolaridade
- **Econômicas**: PIB municipal, índice de desenvolvimento, poder de compra
- **Concorrência**: Número de óticas concorrentes, densidade de POIs relacionados
- **Mobilidade**: Fluxo de pessoas, acessibilidade, transporte público
- **Custos Operacionais**: Aluguel comercial, custos logísticos regionais

**Performance e Validação:**

&ensp;O modelo final apresentou métricas satisfatórias para priorização estratégica:

| Métrica | Valor Obtido | Interpretação | Status |
|---------|--------------|---------------|---------|
| **R² (Teste)** | 0.876 | 87.6% da variância explicada | Satisfatório |
| **AUC** | 0.938 | Excelente capacidade discriminativa | Excelente |
| **Silhouette Score** | 0.364 | Separação moderada dos clusters | Aceitável |
| **Cross-Validation** | Estável | Baixo risco de overfitting | Validado |

**Conclusões Técnicas:**

&ensp;A abordagem híbrida (K-Means com K=4 + Gradient Boosting) demonstrou eficácia na geração de rankings acionáveis para decisões de expansão:

1. **Segmentação Efetiva**: Os 4 clusters identificados capturam perfis distintos de mercado, fornecendo contexto estratégico para as predições

2. **Capacidade Preditiva**: O R² de 0.876 no conjunto de teste indica que o modelo explica adequadamente a variabilidade nos dados de performance

3. **Discriminação Robusta**: AUC de 0.938 confirma alta capacidade de distinguir entre cidades de alto e baixo potencial

4. **Estabilidade**: Validação cruzada consistente reduz riscos de overfitting e aumenta confiança nas predições

**Considerações de Negócio:**

&ensp;Apesar das limitações técnicas identificadas, o modelo atende aos requisitos estratégicos da Chilli Beans:

- **Priorização Relativa**: Rankings consistentes para alocação de capital
- **Segmentação de Mercado**: Insights estruturais sobre perfis de cidades
- **Escalabilidade**: Pipeline reproduzível para novos dados
- **Interpretabilidade**: Resultados compreensíveis para stakeholders

&ensp;A implementação deve ser acompanhada de validação contínua em campo, com cautela contra sobreajuste e monitoramento regular da qualidade das predições conforme novos dados são incorporados ao sistema.

### <a name="c4.4"></a>4.4. Comparação de Modelos

```
- Descrever e justificar a escolha da métrica de avaliação dos modelos com base no que é mais importante para o problema ao 
  se medir a qualidade desses modelos;
- Descrever ao menos três modelos candidatos, seus respectivos algoritmos, seus tunings de hiperparâmetros e suas métricas 
  alcançadas;

Remova este bloco ao final
```

### <a name="c4.5"></a>4.5.Avaliação

```
- Descreva a solução final de modelo preditivo e justifique a escolha. Alinhe sua justificativa com a Seção 4.1, resgatando o entendimento 
  do negócio e das personas, explicando de que formas seu modelo atende os requisitos e definições. 
- Descreva também um plano de contingência para os casos em que o modelo falhar em suas predições.
- Além disso, discuta sobre a explicabilidade do modelo (se aplicável) e realize a verificação de aceitação ou refutação das hipóteses.
- Se aplicável, utilize equações, tabelas e gráficos de visualização de dados para melhor ilustrar seus argumentos. 

Remova este bloco ao final
```

## <a name="c5"></a>5. Conclusões e Recomendações

```
Escreva, de forma resumida, sobre os principais resultados do seu projeto e faça recomendações formais ao seu parceiro de negócios em relação ao uso desse modelo. Você pode aproveitar este espaço para comentar sobre possíveis materiais extras, como um manual de usuário mais detalhado na seção “Anexos”. Não se esqueça também das pessoas que serão potencialmente afetadas pelas decisões do modelo preditivo e elabore recomendações que ajudem seu parceiro a tratá-las de maneira estratégica e ética. 

Remova este bloco ao final
```

## <a name="c6"></a>6. Referências

```
Incluir as principais referências de seu projeto, para que seu parceiro possa consultar caso ele se interessar em aprofundar. Não se esqueça de formatar as referências conforme a ABNT.

Remova este bloco ao final
```

## <a name="attachments"></a>Anexos

### Distribuição normal e teste de hipótese

&ensp;Esta seção avalia a conformidade das variáveis quantitativas com a distribuição normal e formaliza o impacto estatístico dessa verificação sobre as etapas subsequentes de modelagem. Em um projeto preditivo orientado a decisões de negócio (expansão de óticas, segmentação de clientes e aumento da penetração de produtos de grau), a robustez dos resultados depende da escolha correta de: (1) testes estatísticos (paramétricos vs. não‑paramétricos), (2) técnicas de transformação e tratamento de outliers e (3) estratégias de escalonamento que preservem comparabilidade entre atributos de magnitudes distintas. Assim, a identificação de assimetria, curtose e presença de valores extremos direciona tanto a engenharia de atributos quanto a seleção de algoritmos sensíveis à escala (p.ex. K-Means, SVM, redes neurais) ou a supostos distributivos (p.ex. modelos lineares com inferência clássica).

&ensp;Dessa forma, a seção estabelece a fundação estatística que garante que as decisões de modelagem sejam coerentes com a realidade distributiva dos dados, mitigando risco de conclusões enviesadas e maximizando a utilidade prática dos modelos preditivos para a Chilli Beans.

#### Seleção e Caracterização das Variáveis

&ensp;As três variáveis quantitativas selecionadas para o análise foram escolhidas com base em sua relevância para o negócio e potencial impacto em modelos preditivos:

- **Valor_Total**: Representa o valor monetário total da transação, sendo uma variável-chave para análises de receita e comportamento de compra
- **Quantidade**: Indica o número de itens adquiridos em cada transação, fundamental para compreender padrões de consumo
- **Preco_Custo**: Refere-se ao custo de produção dos produtos, essencial para análises de margem e rentabilidade

---

#### Análise e teste de normalidade das variáveis quantitativas

&ensp; A verificação da normalidade dos dados constitui etapa fundamental na análise estatística, uma vez que determina a escolha entre métodos paramétricos e não-paramétricos nas análises subsequentes. Para este estudo, formulou-se a seguinte afirmação a ser testada: as variáveis quantitativas do conjunto de dados seguem uma distribuição normal.

<div align="center">

  **Hipótese Nula** (H₀): Os dados seguem uma distribuição normal</br>
  
  $H_0: X \sim N(\mu, \sigma^2)$

  **Hipótese Alternativa** (Hₐ): Os dados não seguem uma distribuição normal

  $H_1: X \not\sim N(\mu, \sigma^2)$
  
</div>
</br>

&ensp; As hipóteses estatísticas foram estabelecidas conforme a estrutura clássica de testes de hipóteses. A hipótese nula (H₀) postula que os dados seguem uma distribuição normal, enquanto a hipótese alternativa (H₁) estabelece que os dados não seguem uma distribuição normal. Esta formulação permite testar formalmente se cada variável possui distribuição normal, sendo fundamental para a escolha adequada de métodos estatísticos nas etapas posteriores da análise.

##### Nível de Significância e Critérios de Decisão

&ensp; O nível de significância adotado foi α = 0,05 (5%), valor amplamente aceito na literatura científica por representar um equilíbrio adequado entre o risco de erro tipo I e o poder do teste. A relação estabelecida com o valor-p segue o critério padrão de decisão: quando p-valor ≤ 0,05, rejeita-se H₀, indicando evidência estatística de não-normalidade; quando p-valor > 0,05, não se rejeita H₀, sugerindo ausência de evidência suficiente contra a normalidade dos dados.

##### Testes de Normalidade

&ensp; Para verificar a normalidade das variáveis quantitativas, foi aplicado o teste de Shapiro-Wilk, apropriado para amostras com tamanho moderado e amplamente utilizado para avaliação de normalidade. A estatística do teste é definida por:

$$W = \frac{\left( \sum_{i=1}^{n} a_i x_{(i)} \right)^2}{\sum_{i=1}^{n} (x_i - \bar{x})^2}$$

&ensp; Onde $x_{(i)}$​ são os valores ordenados da amostra, $\bar{x}$ é a média amostral, $n$ é o tamanho da amostra, e $a_i$​ são constantes geradas a partir das médias, variâncias e covariâncias das estatísticas de ordem de uma amostra de tamanho $n$ de uma distribuição normal.

| Variável | Estatística | p-valor | Conclusão | Normalidade |
| :--- | :---: | :---: | :--- | :---: |
| **Quantidade** | 0.4043 | 0.00e+00 | Rejeita H₀ | Não-normal |
| **Valor_Total** | 0.6987 | 0.00e+00 | Rejeita H₀ | Não-normal |
| **Preco_Custo** | 0.7065 | 0.00e+00 | Rejeita H₀ | Não-normal |

&ensp; Os resultados indicam que as variáveis Quantidade, Valor_Total, Preco_Custo apresentam evidências de não seguirem uma distribuição normal (p < 0.05).

&ensp;Todos os testes foram feitos no notebook [teste_normalidade.ipynb](../notebooks/teste_normalidade.ipynb).

##### Análise Visual por Histogramas

&ensp; Para complementar a análise estatística, foram construídos histogramas das três variáveis, permitindo uma avaliação visual da distribuição dos dados.

<div align="center">
  <sub>Figura x - Histograma Análise Estatística</sub><br>
  <img src="../assets/histograma1.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

<div align="center">
  <sub>Figura x - Histograma com curva normal sobreposta</sub><br>
  <img src="../assets/histograma2.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;A análise visual dos histogramas confirma os resultados dos testes estatísticos, revelando características específicas de cada distribuição.
&ensp; A variável **Quantidade** apresenta uma distribuição altamente concentrada em valores baixos com presença de outliers, caracterizando uma distribuição fortemente assimétrica à direita.
&ensp; A variável **Valor_Total** exibe uma distribuição com assimetria à direita moderada, presença de valores extremos e uma cauda longa, explicando tanto a rejeição da normalidade quanto a diferença observada entre média e mediana.
&ensp; A variável **Preco_Custo** apresenta uma distribuição relativamente mais concentrada ao redor da mediana, mas com presença de outliers e ligeira assimetria à direita.

A curva normal teórica usada como referência é dada por:

$$
f(x)=\frac{1}{\sigma\sqrt{2\pi}}\,e^{-\frac{(x-\mu)^2}{2\sigma^2}}
$$

em que $\mu$ é a média e $\sigma$ o desvio padrão. A sobreposição dessa curva aos histogramas facilita a visualização dos desvios observados em todas as variáveis.

##### Comparação entre Média e Mediana

&ensp; A comparação entre média e mediana constitui uma ferramenta adicional para avaliar a normalidade das variáveis, uma vez que em distribuições normais esses valores tendem a ser aproximadamente iguais. A média amostral é calculada por:
$$\bar{x} = \frac{1}{n}\sum_{i=1}^{n} x_i$$

&ensp; A mediana é definida como o valor que divide a distribuição ordenada ao meio:

$$\text{Mediana} = \begin{cases}
x_{\frac{n+1}{2}}, & \text{se } n \text{ é ímpar} \\
\frac{x_{\frac{n}{2}} + x_{\frac{n}{2}+1}}{2}, & \text{se } n \text{ é par}
\end{cases}$$

&ensp; A diferença percentual entre média e mediana é calculada por:

$$\text{Diferença (\%)} = \frac{|\bar{x} - \text{Mediana}|}{\text{Mediana}} \times 100$$

##### Tabela de Comparação entre Média e Mediana

| Variável | Média | Mediana | Diferença Absoluta | Diferença (%) |
| :--- | :---: | :---: | :--- | :---: |
| **Quantidade** | 1.02 | 1.00 | 0.02 | 2.49 |
| **Valor_Total** | 267.28| 299.98 | 32.70 | 10.90 |
| **Preco_Custo** | 76.81 | 73.40 | 3.41 | 4.65 |

&ensp;Os resultados apresentam um cenário interessante que requer interpretação cuidadosa. Embora os testes formais de Shapiro-Wilk tenham rejeitado unanimemente a hipótese de normalidade (p < 0.001 para todas as variáveis), a análise das diferenças entre média e mediana revela diferentes graus de assimetria.

&ensp; As variáveis **Quantidade (2.49%)** e **Preco_Custo (4.65%)** apresentam diferenças relativamente pequenas entre média e mediana, próximas do critério de 5% frequentemente associado a distribuições simétricas, enquanto **Valor_Total (10.90%)** exibe diferença mais acentuada.

&ensp;Esta aparente discordância entre os métodos de avaliação indica que os desvios da normalidade detectados pelos testes de Shapiro-Wilk não estão exclusivamente relacionados à assimetria das distribuições. Para as variáveis Quantidade e Preco_Custo, os desvios podem estar associados principalmente a características como curtose inadequada, presença de outliers ou outras violações dos pressupostos de normalidade que não se manifestam necessariamente através de grande assimetria. A variável Valor_Total, por sua vez, apresenta tanto evidência estatística de não-normalidade quanto assimetria mais pronunciada, sugerindo um padrão distributivo mais claramente não-normal.

### Teste de Normalidade com Dados Filtrados

&ensp; Esta seção examina comparativamente os resultados dos testes de normalidade aplicados aos dados originais e os dados após aplicação dos filtros. A comparação visa demonstrar como o processo de filtragem pode impactar as características distributivas das variáveis quantitativas e, consequentemente, influenciar as decisões metodológicas subsequentes no projeto.

&ensp;  O processo de filtragem de dados, conforme documentado anteriormente, removeu 54,41% dos registros originais (de 40.291 para 18.367 registros), eliminando transações de devolução, registros com preços inconsistentes, clientes com idades inválidas, duplicatas e outros dados problemáticos. Esta redução substancial do dataset levanta questões importantes sobre como essas exclusões afetam a normalidade das distribuições das variáveis-chave.

#### Metodologia Comparativa

&ensp; Para esta análise, foram aplicados os mesmos testes de normalidade (Shapiro-Wilk) às três variáveis quantitativas estratégicas nas duas condições:

- **Dados não filtrados:** Dataset original com 40.291 registros
- **Dados filtrados:** Dataset após aplicação dos filtros de negócio com 18.367 registros

#### Resultados Comparativos dos Testes de Normalidade

- **Dados Não Filtrados**

| Variável | Estatística | p-valor | Conclusão | Normalidade |
| :--- | :---: | :---: | :--- | :---: |
| **Quantidade** | 0.4043 | 0.0 | Rejeita H₀ | Não-normal |
| **Valor_Total** | 0.6987 | 0.0 | Rejeita H₀ | Não-normal |
| **Preco_Custo** | 0.7065 | 0.0 | Rejeita H₀ | Não-normal |

- **Dados Filtrados**

| Variável | Estatística | p-valor | Conclusão | Normalidade |
| :--- | :---: | :---: | :--- | :---: |
| **Quantidade** | 0.3987 | 0.0 | Rejeita H₀ | Não-normal |
| **Valor_Total** | 0.7156 | 0.0 | Rejeita H₀ | Não-normal |
| **Preco_Custo** | 0.7234 | 0.0 | Rejeita H₀ | Não-normal |

&ensp;  Embora as mudanças sejam relativamente pequenas, elas indicam que, na variável **Quantidade**, a filtragem concentrou ainda mais a distribuição em valores baixos, intensificando a não-normalidade. Já nas variáveis **Valor_Total** e **Preco_Custo**, a remoção de registros problemáticos resultou em distribuições ligeiramente mais próximas da normalidade, embora ainda significativamente distantes dela.

<div align="center">
  <sub>Figura x - Histogramas e QQ-plots com Dados Filtrados</sub><br>
  <img src="../assets/grafico-filtrado.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp; A comparação entre os testes de normalidade com dados originais e filtrados demonstra que, embora a filtragem tenha melhorado a qualidade geral dos dados e produzido ligeiras mudanças nas estatísticas do teste, as características fundamentalmente não-normais das distribuições permaneceram inalteradas.

&ensp; Este resultado valida a robustez das conclusões sobre normalidade e reforça a necessidade de empregar métodos estatísticos e de machine learning apropriados para dados com distribuições assimétricas e presença de outliers. A filtragem, portanto, cumpriu seu objetivo principal de melhorar a qualidade dos dados sem artificialmente alterar suas características distributivas naturais, preservando a integridade da análise estatística subsequente.

### Comparação entre Testes de Normalidade: Shapiro-Wilk vs. Kolmogorov-Smirnov

&ensp;Para fortalecer a robustez da análise de normalidade, foram aplicados dois testes estatísticos distintos: Shapiro-Wilk e Kolmogorov-Smirnov. Esta abordagem comparativa permite validar os resultados e compreender melhor as características distributivas das variáveis quantitativas estratégicas do projeto.

#### Fundamentação Teórica dos Testes

**Teste de Shapiro-Wilk:**
&ensp;O teste de Shapiro-Wilk é considerado um dos testes mais poderosos para detecção de desvios da normalidade, especialmente eficaz para amostras de tamanho pequeno a moderado (n ≤ 5000). Sua estatística W mede a correlação entre os dados ordenados e os quantis teóricos da distribuição normal, sendo mais sensível a desvios na forma da distribuição.

**Teste de Kolmogorov-Smirnov:**
&ensp;O teste de Kolmogorov-Smirnov (KS) avalia a máxima diferença entre a função de distribuição empírica e a função de distribuição normal teórica. É menos sensível a outliers isolados, mas eficaz na detecção de diferenças sistemáticas na forma da distribuição. A estatística KS é definida por:

$$D = \max_{x} |F_n(x) - F_0(x)|$$

&ensp;Onde $F_n(x)$ é a função de distribuição empírica e $F_0(x)$ é a função de distribuição normal teórica.

#### Resultados Comparativos

##### Dados Originais (40.291 registros)

| Variável | Shapiro-Wilk |  | Kolmogorov-Smirnov |  | Concordância |
|----------|:------------:|:--:|:------------------:|:--:|:------------:|
|          | Estatística | p-valor | Estatística | p-valor | Conclusão |
| **Quantidade** | 0.4043 | 0.0 | 0.4721 | 0.0 | Concordam |
| **Valor_Total** | 0.6987 | 0.0 | 0.1892 | 0.0 | Concordam |
| **Preco_Custo** | 0.7065 | 0.0 | 0.1647 | 0.0 | Concordam |

##### Dados Filtrados (18.367 registros)

| Variável | Shapiro-Wilk |  | Kolmogorov-Smirnov |  | Concordância |
|----------|:------------:|:--:|:------------------:|:--:|:------------:|
|          | Estatística | p-valor | Estatística | p-valor | Conclusão |
| **Quantidade** | 0.3987 | 0.0 | 0.4823 | 0.0 | Concordam |
| **Valor_Total** | 0.7156 | 0.0 | 0.1721 | 0.0 | Concordam |
| **Preco_Custo** | 0.7234 | 0.0 | 0.1534 | 0.0 | Concordam |

#### Interpretação dos Resultados

**Concordância Unanime:**
&ensp;Ambos os testes rejeitaram consistentemente a hipótese nula de normalidade para todas as variáveis, tanto nos dados originais quanto nos filtrados ($p < 0.001$ em todos os casos). Esta concordância robusta confirma que as variáveis apresentam desvios significativos da distribuição normal.

**Padrões Observados:**

1. **Quantidade:** Apresenta as maiores estatísticas KS ($0.4721$ e $0.4823$), indicando desvios mais pronunciados da normalidade. Isto corrobora a observação visual de distribuição altamente concentrada em valores baixos.

2. **Valor_Total:** Mostra melhoria ligeira após filtragem (KS de $0.1892$ para $0.1721$), sugerindo que a remoção de registros problemáticos reduziu marginalmente os desvios da normalidade.

3. **Preco_Custo:** Exibe a maior melhoria relativa após filtragem (KS de $0.1647$ para $0.1534$), indicando que a limpeza dos dados foi mais efetiva para esta variável.

#### Sensibilidade Diferencial dos Testes

&ensp;Embora ambos os testes concordem na rejeição da normalidade, eles capturam aspectos complementares dos desvios:

- **Shapiro-Wilk:** Mais sensível a desvios na curtose e assimetria global da distribuição
- **Kolmogorov-Smirnov:** Mais focado em diferenças sistemáticas na função de distribuição acumulada

&ensp;A variável **Quantidade** apresenta baixos valores W ($0.3987-0.4043$) no teste Shapiro-Wilk, indicando forte desvio da normalidade, enquanto altos valores D ($0.4721-0.4823$) no teste KS confirmam grandes discrepâncias na distribuição acumulada.

#### Implicações para Modelagem

&ensp;A confirmação robusta de não-normalidade através de dois testes independentes tem implicações práticas importantes:

1. **Escolha de Algoritmos:** Favorece modelos não-paramétricos ou que não assumem normalidade (árvores de decisão, Random Forest, modelos baseados em distância)

2. **Transformações de Dados:** Justifica a aplicação de transformações (log, Box-Cox) ou técnicas de normalização robustas

3. **Métodos Estatísticos:** Orienta para testes não-paramétricos em análises de significância e correlação

#### Conclusão da Análise Comparativa

&ensp;A aplicação de dois testes de normalidade distintos fortalece significativamente a confiabilidade das conclusões. A concordância completa entre Shapiro-Wilk e Kolmogorov-Smirnov, tanto para dados originais quanto filtrados, confirma de forma inequívoca que as três variáveis quantitativas estratégicas não seguem distribuição normal.

&ensp;Esta confirmação robusta orienta as decisões metodológicas subsequentes do projeto, garantindo que as técnicas de modelagem preditiva selecionadas sejam apropriadas para dados com distribuições assimétricas e presença de outliers, maximizando assim a precisão e confiabilidade dos modelos desenvolvidos para

---

#### Escalonamento nas variáveis quantitativas

&ensp;O escalonamento de variáveis quantitativas constitui uma etapa fundamental no pré-processamento de dados para modelagem preditiva, especialmente quando se trabalha com algoritmos de machine learning que são sensíveis à escala das variáveis. Esta técnica visa transformar as variáveis para que possuam escalas comparáveis, evitando que variáveis com magnitudes maiores dominem o processo de aprendizado do modelo e comprometam sua capacidade preditiva.

&ensp;No contexto da análise de dados da Chilli Beans, as três variáveis quantitativas estratégicas escolhidas foram: **Valor_Total**, **Quantidade** e **Preco_Custo**. Essas variáveis representam aspectos fundamentais das transações comerciais e possuem escalas naturalmente distintas, tornando o escalonamento essencial para análises posteriores e desenvolvimento de modelos preditivos eficazes.

&ensp;O escalonamento adequado permite que algoritmos de clustering, classificação e regressão processem os dados de forma mais equilibrada, melhorando a convergência dos modelos e a qualidade das predições. Além disso, facilita a interpretação de resultados e a comparação entre diferentes variáveis em análises exploratórias.

&ensp;A análise exploratória inicial revelou características distintas para cada variável. O dataset filtrado contém 18.367 registros válidos, após aplicação das regras de negócio estabelecidas. As estatísticas descritivas demonstram a necessidade de escalonamento devido às diferentes magnitudes e distribuições das variáveis.

<div align="center">
  <sub>Figura X - Distribuições das Variáveis Originais</sub><br>
  <img src="../assets/distribuicoes_originais.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;Todo o processo de escalonamento foi implementado no notebook [data_scaling_analysis.ipynb](../notebooks/data_scaling_analysis.ipynb).

##### Análise Estatística e Determinação do Método de Escalonamento

&ensp;Para determinar o método de escalonamento mais adequado para cada variável, foi realizada uma análise detalhada das características estatísticas, considerando a presença de outliers, assimetria da distribuição e coeficiente de variação.

| Variável     | Mínimo    | Máximo      | Média    | Desvio Padrão (pop.) | Assimetria | Outliers (n / %) | Coef. Variação |
|--------------|-----------|-------------|----------|----------------------|------------|------------------|----------------|
| Valor_Total  | R$ 0,01   | R$ 11.057,97| R$ 305,63| 316,61               | 7,056      | 383 (2,09%)      | 103,60%        |
| Quantidade   | 1         | 12          | 1,09     | 0,326                | 6,337      | 1.536 (8,36%)    | 29,90%         |
| Preco_Custo  | R$ 1,80   | R$ 1.379,16 | R$ 76,37 | 75,19                | 4,440      | 402 (2,19%)      | 98,46%         |

&ensp;Com base nesta análise, foi determinado que todas as três variáveis apresentam características que favorecem o uso da **padronização (standardization)** em detrimento da normalização (min-max scaling). Os critérios decisivos foram:

1. **Alta assimetria**: Todas as variáveis apresentam valores de skewness superiores a 1, indicando distribuições fortemente assimétricas
2. **Presença de outliers**: Especialmente na variável Quantidade, com 8,36% de outliers
3. **Alta variabilidade**: Coeficientes de variação elevados, particularmente em Valor_Total (103,60%) e Preco_Custo (98,46%)

&ensp;A padronização (Z-score) é mais robusta a outliers e distribuições assimétricas, transformando os dados para uma distribuição com média zero e desvio padrão unitário, preservando a forma da distribuição original enquanto torna as escalas comparáveis.

##### Equações Matemáticas de Escalonamento

&ensp;Para cada variável, foi aplicada a fórmula de padronização (Z-score), que transforma os valores originais subtraindo a média e dividindo pelo desvio padrão populacional. As equações específicas, com os valores calculados a partir do dataset completo, são apresentadas a seguir:

**Valor_Total:**

- Fórmula geral: $Z = (X - \mu) / \sigma$
- Fórmula específica: $Z = (X - 305.629263) / 316.612146$

**Quantidade:**

- Fórmula geral: $Z = (X - \mu) / \sigma$
- Fórmula específica: $Z = (X - 1.090216) / 0.325962$

**Preco_Custo:**

- Fórmula geral: $Z = (X - \mu) / \sigma$
- Fórmula específica: $Z = (X - 76.367664) / 75.191564$

&ensp;Onde X representa o valor original da variável, $\mu$ (mu) é a média populacional e $\sigma$ (sigma) é o desvio padrão populacional. Após a transformação, todas as variáveis passam a ter média aproximadamente igual a zero e desvio padrão igual a um, tornando-as diretamente comparáveis.

##### Implementação e Validação do Escalonamento

&ensp;O processo de escalonamento foi implementado utilizando a biblioteca scikit-learn, especificamente a classe StandardScaler, que garante a aplicação correta da fórmula de padronização. A validação foi realizada verificando-se que as variáveis transformadas apresentam as características esperadas:

- **Média próxima a zero**: Todas as variáveis escalonadas apresentaram média inferior a 10⁻⁶
- **Desvio padrão unitário**: Todas as variáveis escalonadas apresentaram desvio padrão igual a 1,0
- **Preservação da distribuição**: A forma das distribuições foi mantida, apenas alterando a escala

##### Comparação Visual: Antes e Depois do Escalonamento

&ensp;A análise visual das distribuições antes e depois do escalonamento demonstra claramente o efeito da transformação. Os histogramas comparativos revelam que, embora a forma das distribuições seja preservada, as escalas tornam-se padronizadas, facilitando análises posteriores.

<div align="center">
  <sub>Figura X - Comparação das Distribuições Antes e Depois do Escalonamento</sub><br>
  <img src="../assets/histogramas_comparacao.png"><br>
  <sup>Fonte: Material produzido pelos autores (2025)</sup>
</div>

&ensp;Os histogramas demonstram que:

1. **Valor_Total**: A distribuição mantém sua característica assimétrica positiva, mas agora está centrada em zero
2. **Quantidade**: A distribuição discreta é preservada, com a concentração em valores baixos mantida
3. **Preco_Custo**: A distribuição assimétrica é mantida, mas com escala padronizada

##### Análise Comparativa dos Dados

&ensp;Para ilustrar concretamente o efeito do escalonamento, são apresentadas as tabelas comparativas com os primeiros 10 registros do dataset, mostrando os valores originais e seus correspondentes escalonados.

**Tabela 1: Dados Originais (Primeiros 10 registros)**

| Registro | Valor_Total | Quantidade | Preco_Custo |
|----------|-------------|------------|-------------|
| 1        | 20.968      | 1          | 7.00        |
| 2        | 63.000      | 1          | 53.67       |
| 3        | 58.000      | 1          | 53.67       |
| 4        | 70.000      | 1          | 53.67       |
| 5        | 63.000      | 1          | 53.67       |
| 6        | 53.000      | 1          | 47.06       |
| 7        | 20.000      | 1          | 7.00        |
| 8        | 16.396      | 1          | 7.00        |
| 9        | 19.980      | 1          | 5.22        |
| 10       | 119.880     | 6          | 5.22        |

**Tabela 2: Dados Escalonados (Primeiros 10 registros)**

| Registro | Valor_Total (Escalonado) | Quantidade (Escalonado) | Preco_Custo (Escalonado) |
|----------|--------------------------|-------------------------|--------------------------|
| 1        | -0.899085               | -0.276769               | -0.922546                |
| 2        | -0.766330               | -0.276769               | -0.301864                |
| 3        | -0.782122               | -0.276769               | -0.301864                |
| 4        | -0.744221               | -0.276769               | -0.301864                |
| 5        | -0.766330               | -0.276769               | -0.301864                |
| 6        | -0.797914               | -0.276769               | -0.389773                |
| 7        | -0.902142               | -0.276769               | -0.922546                |
| 8        | -0.913524               | -0.276769               | -0.922546                |
| 9        | -0.902206               | -0.276769               | -0.946219                |
| 10       | -0.586678               | 15.062459               | -0.946219                |

&ensp;A análise das tabelas revela aspectos importantes do escalonamento:

1. **Valores negativos e positivos**: Os valores escalonados podem ser negativos (quando menores que a média original) ou positivos (quando maiores que a média original)
2. **Magnitude dos valores**: Valores próximos à média original resultam em valores escalonados próximos a zero
3. **Outliers preservados**: O registro 10 da variável Quantidade (valor original 6) resulta em um valor escalonado alto ($15.06$), indicando que se trata de um outlier significativo
4. **Comparabilidade**: Todas as variáveis agora possuem a mesma escala, permitindo comparações diretas

&ensp;A análise de escalonamento das variáveis quantitativas **Valor_Total**, **Quantidade** e **Preco_Custo** demonstrou a importância desta etapa no pré-processamento de dados para modelagem preditiva. A escolha da padronização (Z-score) como método de escalonamento mostrou-se adequada para todas as três variáveis, considerando suas características estatísticas específicas. O escalonamento adequado das variáveis quantitativas contribuirá significativamente para:

- **Melhoria da convergência**: Algoritmos de otimização convergem mais rapidamente quando as variáveis estão em escalas similares
- **Estabilidade numérica**: Redução de problemas de precisão numérica em cálculos computacionais
- **Interpretabilidade**: Facilita a comparação da importância relativa das variáveis nos modelos
- **Performance dos modelos**: Melhora o desempenho de algoritmos sensíveis à escala, como redes neurais, SVM e k-means

&ensp;Os resultados obtidos confirmam que o escalonamento foi aplicado corretamente, com todas as variáveis transformadas apresentando média próxima a zero e desvio padrão unitário. Esta transformação é fundamental para garantir que algoritmos de machine learning possam processar os dados de forma equilibrada, sem que variáveis com magnitudes maiores dominem o processo de aprendizado.
