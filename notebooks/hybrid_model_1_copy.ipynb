# CÉLULA 1: PREPARAÇÃO E SEGMENTAÇÃO GEOGRÁFICA (VERSÃO COMPLETA E CORRIGIDA)
import sys
import os

# Adicionar o diretório pai ao path para importar o módulo de filtragem
sys.path.append(os.path.dirname(os.path.abspath('.')))

# --- 1. Carregar e Aplicar Filtros de Qualidade ---
try:
    from data_filtering import apply_business_filters
    print("Módulo de filtragem importado com sucesso!")
except ImportError:
    print("Erro: Arquivo 'data_filtering.py' não encontrado.")
    # Se o módulo não for encontrado, interrompemos a execução.
    exit()

print("Carregando e aplicando filtros de qualidade...")
# Esta linha define o dataframe 'df'
df = apply_business_filters('../assets/database.csv')
print(f"Registros após a filtragem inicial: {len(df)}")

# --- 2. Aplicar Filtro de Cidades com > 5 Clientes ---
print("\nAplicando filtro de cidades com mais de 5 clientes únicos...")
try:
    # Agora o 'df' existe e pode ser usado aqui
    clientes_por_cidade = df.groupby('Dim_Cliente.Cidade_cliente')['ID_Cliente'].nunique()
    cidades_validas = clientes_por_cidade[clientes_por_cidade > 5].index
    df_filtrado_cidade = df[df['Dim_Cliente.Cidade_cliente'].isin(cidades_validas)].copy()
    print(f"Registros após filtro de cidades: {len(df_filtrado_cidade)}")
except KeyError:
    print("\nERRO: A coluna 'Dim_Cliente.Cidade_cliente' não foi encontrada. Verifique o nome.")
    exit()

# --- 3. Dividir por Macrorregião (SP vs. Resto do Brasil) ---
print("\nDividindo a base de dados em São Paulo vs. Resto do Brasil...")
is_sp = df_filtrado_cidade['Dim_Cliente.Regiao_Cliente'].str.contains("PAULO|PAULISTA", case=False, na=False)

df_sp = df_filtrado_cidade[is_sp].copy()
df_resto_br = df_filtrado_cidade[~is_sp].copy()

print("\n--- Segmentação Geográfica Concluída ---")
print(f"Total de Registros Válidos: {len(df_filtrado_cidade)}")
print(f"Registros em São Paulo: {len(df_sp)} ({len(df_sp)/len(df_filtrado_cidade):.1%})")
print(f"Registros no Resto do Brasil: {len(df_resto_br)} ({len(df_resto_br)/len(df_filtrado_cidade):.1%})")

# CÉLULA 2 (VERSÃO PLATINUM): FUNÇÃO FINAL COM FEATURES AVANÇADAS

import pandas as pd
import numpy as np

def cria_perfil_historico_cliente(df_input, data_referencia):
    """
    Versão final que transforma transações em perfis de clientes únicos,
    com features avançadas de comportamento, incluindo afinidade por tipo de PDV.
    """
    df = df_input.copy()
    df['ID_Date'] = pd.to_datetime(df['ID_Date'])
    df['Dia_Semana'] = df['ID_Date'].dt.dayofweek # 0=Segunda, 6=Domingo

    # --- 1. Criar Flags de Categoria, Desconto, Canal e TIPO DE PDV ---
    def is_grau(x): s = str(x).upper(); return 1 if "GRAU" in s or "VISTA" in s or "LENTE" in s else 0
    def is_solar(x): s = str(x).upper(); return 1 if "SOLAR" in s else 0
    def is_relogio(x): s = str(x).upper(); return 1 if "RELOGIO" in s else 0
    
    df['is_grau_flag'] = df['Dim_Produtos.Grupo_Produto'].apply(is_grau)
    df['is_solar_flag'] = df['Dim_Produtos.Grupo_Produto'].apply(is_solar)
    df['is_relogio_flag'] = df['Dim_Produtos.Grupo_Produto'].apply(is_relogio)
    
    df['teve_desconto'] = (df['DESCONTO_CALCULADO'] > 0).astype(int)
    df['is_canal_otica'] = (df['Dim_Lojas.CANAL_VENDA'] == 'OTICA').astype(int)
    
    # Novas flags para Tipo de PDV
    df['is_loja_rua'] = df['Dim_Lojas.Tipo_PDV'].str.contains('RUA', case=False, na=False).astype(int)
    df['is_quiosque'] = df['Dim_Lojas.Tipo_PDV'].str.contains('QUIOSQUE', case=False, na=False).astype(int)
    df['is_loja_shopping'] = ((df['Dim_Lojas.Tipo_PDV'].str.contains('LOJA', case=False, na=False)) & (~df['Dim_Lojas.Tipo_PDV'].str.contains('RUA', case=False, na=False))).astype(int)


    # --- 2. Dicionário de Agregações "Platinum" ---
    aggregations = {
        'ID_Date': ['min', 'max'],
        'DOC_UNICO': 'nunique',
        'Valor_Total': ['sum', 'mean'],
        'Quantidade': 'sum',
        'is_grau_flag': 'max',
        'is_solar_flag': 'max',
        'is_relogio_flag': 'max',
        'teve_desconto': 'mean',
        'is_canal_otica': 'mean',
        'is_loja_rua': 'mean',
        'is_quiosque': 'mean',
        'is_loja_shopping': 'mean',
        'ID_Loja': 'nunique',
        'Dia_Semana': (lambda x: x.mode()[0]),
        'Dim_Cliente.Data_Nascimento': 'first'
    }

    df_clientes = df.groupby('ID_Cliente').agg(aggregations).reset_index()

    # --- 3. Ajustar Nomes das Colunas ---
    df_clientes.columns = [
        'ID_Cliente', 'Data_Primeira_Compra', 'Data_Ultima_Compra', 'Frequencia', 
        'Monetaridade_Total', 'Ticket_Medio', 'Quantidade_Total', 
        'Comprou_Grau_Historico', 'Comprou_Solar_Historico', 'Comprou_Relogio_Historico',
        'Pct_Compras_Com_Desconto', 'Pct_Compras_Canal_Otica', 'Pct_Compras_Loja_Rua',
        'Pct_Compras_Quiosque', 'Pct_Compras_Loja_Shopping', 'N_Lojas_Unicas', 
        'Dia_Semana_Preferido', 'Data_Nascimento'
    ]

    # --- 4. Engenharia de Features Finais (Pós-Agregação) ---
    df_clientes['Recencia'] = (data_referencia - df_clientes['Data_Ultima_Compra']).dt.days
    df_clientes['Tempo_Como_Cliente'] = (data_referencia - df_clientes['Data_Primeira_Compra']).dt.days
    df_clientes['Idade'] = (data_referencia - pd.to_datetime(df_clientes['Data_Nascimento'])).dt.days / 365.25
    
    df_clientes['Itens_Por_Transacao'] = df_clientes['Quantidade_Total'] / df_clientes['Frequencia']
    df_clientes['Preco_Medio_Item'] = df_clientes['Monetaridade_Total'] / df_clientes['Quantidade_Total']
    
    df_clientes['Cadencia_Compra'] = df_clientes['Tempo_Como_Cliente'] / (df_clientes['Frequencia'] - 1)
    df_clientes['Cadencia_Compra'].replace([np.inf, -np.inf], 999, inplace=True) # Valor alto para clientes de 1 compra
    df_clientes.fillna({'Cadencia_Compra': 999}, inplace=True) # Preencher NaNs se houver

    # --- 5. Limpeza Final ---
    df_clientes = df_clientes.drop(columns=['Data_Primeira_Compra', 'Data_Ultima_Compra', 'Data_Nascimento'])
    df_clientes = df_clientes.dropna(subset=['Idade'])
    
    return df_clientes

print("Função 'Platinum' cria_perfil_historico_cliente criada com sucesso.")

# CÉLULA 3 (CORRIGIDA): APLICANDO A FUNÇÃO PLATINUM E PREPARANDO PREVISÕES
import gc
import pandas as pd
import numpy as np
from sklearn.preprocessing import MinMaxScaler

# Data de referência (garantir que exista)
try:
    data_referencia
except NameError:
    data_referencia = pd.to_datetime(df_filtrado_cidade['ID_Date'].max())

print(f"Data de referência para os cálculos: {data_referencia.date()}")

# 1. Processar São Paulo
print("\n--- Processando São Paulo ---")
df_clientes_sp = cria_perfil_historico_cliente(df_sp, data_referencia)
print(f"Total de clientes únicos em SP: {len(df_clientes_sp)}")
print("Amostra dos dados de SP:")
print(df_clientes_sp.head())

# 2. Processar Resto do Brasil
print("\n--- Processando Resto do Brasil ---")
df_clientes_resto_br = cria_perfil_historico_cliente(df_resto_br, data_referencia)
print(f"Total de clientes únicos no Resto do Brasil: {len(df_clientes_resto_br)}")
print("Amostra dos dados do Resto do Brasil:")
print(df_clientes_resto_br.head())

# 3. Garantir que o modelo está disponível (fallback amigável)
try:
    predictor = model_final
except NameError:
    print("Aviso: 'model_final' não encontrado. Execute a célula de treinamento do modelo nacional antes desta célula.")
    predictor = None

# 4. Preparar X_pred (features) - preferir X_nacional quando disponível
try:
    X_pred = X_nacional.copy()
except NameError:
    if 'df_clientes_nacional' in globals():
        X_pred = df_clientes_nacional.drop(columns=['ID_Cliente', 'Comprou_Grau_Historico'], errors='ignore').copy()
    else:
        X_pred = None

# 5. Calcular probabilidades (fallback seguro para modelos sem predict_proba)
probs = np.array([])
if predictor is not None and X_pred is not None:
    try:
        probs = predictor.predict_proba(X_pred)[:, 1]
    except Exception:
        try:
            scores = predictor.decision_function(X_pred)
            probs = MinMaxScaler().fit_transform(scores.reshape(-1, 1)).ravel()
        except Exception as e:
            print('Não foi possível obter probabilidades a partir do modelo:', e)
            probs = np.full(shape=(len(X_pred),), fill_value=np.nan)
else:
    print('Não há modelo ou features para prever probabilidades; propensidade será NaN.')
    if X_pred is not None:
        probs = np.full(shape=(len(X_pred),), fill_value=np.nan)

# 6. Anexar probabilidades ao dataframe nacional (se existir)
if 'df_clientes_nacional' in globals():
    try:
        if len(probs) == len(df_clientes_nacional):
            df_clientes_nacional['propensidade_grau'] = probs
        else:
            # tentar alinhar por índice de X_pred se possível
            try:
                df_clientes_nacional = df_clientes_nacional.assign(propensidade_grau=pd.Series(probs, index=X_pred.index))
            except Exception:
                df_clientes_nacional['propensidade_grau'] = np.nan
                print('Tamanho de probabilidades não corresponde; propensidade preenchida com NaN.')
    except Exception as e:
        print('Erro ao anexar propensidade ao dataframe nacional:', e)
else:
    print('df_clientes_nacional não encontrado; não foi possível anexar propensidade.')

# 7. Preparar colunas para exibição (pegar features presentes)
present_features = []
if 'df_clientes_nacional' in globals():
    cand = ['Idade','Frequencia','Monetaridade_Total','Ticket_Medio','N_Lojas_Unicas','Recencia','Cadencia_Compra']
    present_features = [c for c in cand if c in df_clientes_nacional.columns]

display_cols = present_features + ['propensidade_grau']

# 8. Resumo por cluster
if 'df_clientes_nacional' in globals() and 'Cluster_Nacional' in df_clientes_nacional.columns:
    cluster_summary = df_clientes_nacional.groupby('Cluster_Nacional').agg(
        mean_propensidade=('propensidade_grau', 'mean'),
        n_clientes=('ID_Cliente', 'count')
    ).sort_values('mean_propensidade', ascending=False).reset_index()
    print('\n=== Resumo de Propensão por Cluster (ordenado) ===')
    print(cluster_summary)
else:
    print('Coluna Cluster_Nacional não encontrada - será mostrado apenas ranking por cliente.')

# 9. Mostrar os top N perfis com maior probabilidade
TOP_N = 50
if 'df_clientes_nacional' in globals() and 'propensidade_grau' in df_clientes_nacional.columns:
    top_clients = df_clientes_nacional.sort_values('propensidade_grau', ascending=False).head(TOP_N)
    print(f'\nTop {TOP_N} perfis por propensão a comprar GRAU (principais características):')
    if len(display_cols) == 0:
        print('Nenhuma característica selecionada encontrada no dataframe.')
    else:
        out = top_clients[display_cols].copy()
        out['propensidade_grau'] = (out['propensidade_grau'] * 100).round(2).astype(str) + '%'
        print(out.to_string(index=False))
else:
    print('Não há coluna "propensidade_grau" para ordenar e exibir.')

# 10. Limpeza de memória
gc.collect()

# CÉLULA 4: APLICAÇÃO DO K-MEANS COM k=4 (FEATURES PLATINUM - SÃO PAULO)

# Importações locais para garantir disponibilidade
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import numpy as np

# Garantir lista de features para cluster (reusar global se existir)
features = globals().get('features_para_cluster_platinum')
if not features:
    features = [
        'Frequencia', 'Monetaridade_Total', 'Ticket_Medio', 'Quantidade_Total',
        'Pct_Compras_Com_Desconto', 'Pct_Compras_Canal_Otica', 'Pct_Compras_Loja_Rua',
        'Pct_Compras_Quiosque', 'Pct_Compras_Loja_Shopping', 'N_Lojas_Unicas',
        'Recencia', 'Tempo_Como_Cliente', 'Idade', 'Itens_Por_Transacao',
        'Preco_Medio_Item', 'Cadencia_Compra'
    ]

# Preparar a matriz de features para SP, removendo linhas com NaNs nas features selecionadas
try:
    data_sp_cluster = df_clientes_sp[features].dropna()
except Exception as e:
    raise SystemExit(f"Erro preparando features para cluster em SP: {e}")

if data_sp_cluster.shape[0] == 0:
    raise SystemExit('Nenhum registro válido em df_clientes_sp para as features selecionadas. Verifique os dados e a lista de features.')

# Padronizar
scaler_sp_platinum = StandardScaler()
data_sp_scaled_platinum = scaler_sp_platinum.fit_transform(data_sp_cluster)

# --- 1. Aplicar o Algoritmo K-Means com k=4 ---
# Usamos n_clusters=4, conforme nossa análise do gráfico do cotovelo
kmeans_sp_platinum = KMeans(n_clusters=4, init='k-means++', random_state=42, n_init=10)
clusters_sp_platinum = kmeans_sp_platinum.fit_predict(data_sp_scaled_platinum)

# --- 2. Adicionar os Novos Rótulos de Cluster ao Dataframe ---
# Alinhar os rótulos ao índice original de df_clientes_sp (somente para as linhas sem NaN)
df_clientes_sp.loc[data_sp_cluster.index, 'Cluster_Platinum'] = clusters_sp_platinum

# --- 3. Analisar as Características de Cada Novo Cluster ---
perfil_clusters_sp_platinum = df_clientes_sp.groupby('Cluster_Platinum')[features].mean().round(2)

# Adicionar a contagem de clientes em cada cluster
perfil_clusters_sp_platinum['N_Clientes'] = df_clientes_sp['Cluster_Platinum'].value_counts().reindex(perfil_clusters_sp_platinum.index).fillna(0).astype(int)

print("--- Perfil Médio dos 4 Novos Clusters de Clientes (São Paulo) ---")
# Usamos .T para transpor a tabela, facilitando a leitura quando há muitas colunas
print(perfil_clusters_sp_platinum.T)


# CÉLULA 5: ANÁLISE DA AFINIDADE DOS NOVOS CLUSTERS (SÃO PAULO)

# Agrupamos os clientes pelo novo 'Cluster_Platinum'
analise_afinidad_sp_platinum = df_clientes_sp.groupby('Cluster_Platinum').agg(
    N_Clientes=('ID_Cliente', 'count'),
    Taxa_Comprou_Grau=('Comprou_Grau_Historico', 'mean'),
    Monetaridade_Total_Cluster=('Monetaridade_Total', 'sum')
).round(2)

# Formatando para percentual
analise_afinidad_sp_platinum['Taxa_Comprou_Grau'] = (analise_afinidad_sp_platinum['Taxa_Comprou_Grau'] * 100).map('{:.2f}%'.format)

# Ordenando pela taxa de compra de grau
analise_afinidad_sp_platinum = analise_afinidad_sp_platinum.sort_values(by='Taxa_Comprou_Grau', ascending=False)


print("--- Análise de Afinidade dos Novos Clusters com a Categoria Grau (São Paulo) ---")
print(analise_afinidad_sp_platinum)

# CÉLULA 6: MÉTODO DO COTOVELO (FEATURES PLATINUM - RESTO DO BRASIL)

from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt
import numpy as np

# --- 1. Garantir que temos df_clientes_resto_br ---
if 'df_clientes_resto_br' not in globals():
    raise SystemExit("ERRO: Execute primeiro a Célula 3 para criar df_clientes_resto_br")

# --- 2. Selecionar as Novas Features para Clusterização ---
# Reutilizar features_para_cluster_platinum global se existir, caso contrário criar
features = globals().get('features_para_cluster_platinum')
if not features:
    features = [
        'Frequencia', 'Monetaridade_Total', 'Ticket_Medio', 'Quantidade_Total',
        'Pct_Compras_Com_Desconto', 'Pct_Compras_Canal_Otica', 'Pct_Compras_Loja_Rua',
        'Pct_Compras_Quiosque', 'Pct_Compras_Loja_Shopping', 'N_Lojas_Unicas',
        'Recencia', 'Tempo_Como_Cliente', 'Idade', 'Itens_Por_Transacao',
        'Preco_Medio_Item', 'Cadencia_Compra'
    ]
    features_para_cluster_platinum = features

# --- 3. Preparar a Matriz de Features ---
print("Preparando features para clusterização...")
try:
    data_resto_br_cluster = df_clientes_resto_br[features].copy()
    # Remover linhas com valores faltantes
    data_resto_br_cluster_platinum = data_resto_br_cluster.dropna()
    print(f"Shape dos dados após remoção de NaN: {data_resto_br_cluster_platinum.shape}")
except Exception as e:
    raise SystemExit(f"Erro ao preparar features para cluster: {e}")

if data_resto_br_cluster_platinum.shape[0] == 0:
    raise SystemExit("Nenhum registro válido após remoção de NaN")

# --- 4. Padronizar a Escala ---
print("Padronizando as features...")
scaler_platinum_resto_br = StandardScaler()
data_resto_br_scaled_platinum = scaler_platinum_resto_br.fit_transform(data_resto_br_cluster_platinum)

# --- 5. Aplicar o Método do Cotovelo ---
print("\nCalculando o WCSS para 1 a 10 clusters para o 'Resto do Brasil'...")
wcss_platinum_resto_br = []
for i in range(1, 11):
    kmeans = KMeans(n_clusters=i, init='k-means++', random_state=42, n_init=10)
    kmeans.fit(data_resto_br_scaled_platinum)
    wcss_platinum_resto_br.append(kmeans.inertia_)
    print(f"  Cluster {i}: WCSS = {kmeans.inertia_:.2f}")

# --- 6. Plotar o Gráfico do Cotovelo ---
plt.figure(figsize=(10, 6))
plt.plot(range(1, 11), wcss_platinum_resto_br, marker='o', linestyle='--')
plt.title('Método do Cotovelo (Features Platinum) - Clientes do Resto do Brasil')
plt.xlabel('Número de Clusters')
plt.ylabel('WCSS (Inertia)')
plt.grid(True)
plt.show()

# CÉLULA 7: KMEANS PARA RESTO DO BRASIL (5 CLUSTERS)

print("Aplicando KMeans com 5 clusters aos clientes do resto do Brasil...")

# Criar e treinar o modelo
kmeans_resto_br = KMeans(n_clusters=5, init='k-means++', random_state=42, n_init=10)
clusters_resto_br = kmeans_resto_br.fit_predict(data_resto_br_scaled_platinum)

# Adicionar as labels dos clusters ao DataFrame
df_clientes_resto_br.loc[data_resto_br_cluster_platinum.index, 'Cluster'] = clusters_resto_br

# Análise inicial dos clusters
print("\nDistribuição dos clusters:")
cluster_sizes = df_clientes_resto_br['Cluster'].value_counts().sort_index()
for cluster in range(5):
    count = cluster_sizes[cluster]
    percentage = (count / len(clusters_resto_br)) * 100
    print(f"Cluster {cluster}: {count} clientes ({percentage:.1f}%)")

# Calcular os centroides dos clusters nas features originais
centroids_scaled = pd.DataFrame(
    kmeans_resto_br.cluster_centers_,
    columns=features
)

# Análise dos centroides
print("\nCaracterísticas dos centroides (valores padronizados):")
for cluster in range(5):
    print(f"\nCluster {cluster}:")
    centroid = centroids_scaled.iloc[cluster]
    # Mostrar as 3 características mais altas e mais baixas
    print("Top 3 características mais altas:")
    print(centroid.nlargest(3))
    print("\nTop 3 características mais baixas:")
    print(centroid.nsmallest(3))

# Visualização da distribuição dos clusters
plt.figure(figsize=(10, 6))
plt.hist(clusters_resto_br, bins=5, align='left', rwidth=0.8)
plt.title('Distribuição dos Clusters - Resto do Brasil')
plt.xlabel('Número do Cluster')
plt.ylabel('Número de Clientes')
plt.xticks(range(5))
plt.grid(True, alpha=0.3)
plt.show()

# CÉLULA 8: PREPARAÇÃO PARA CLUSTERIZAÇÃO NACIONAL

from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import matplotlib.pyplot as plt

# --- 1. Unificar as Bases de Clientes ---
# Concatenamos os dataframes de SP e Resto do Brasil
try:
    df_clientes_nacional = pd.concat([df_clientes_sp, df_clientes_resto_br], ignore_index=True)
    print(f"Bases de clientes unificadas. Total de clientes únicos no Brasil: {len(df_clientes_nacional)}")
except NameError:
    print("ERRO: Os dataframes 'df_clientes_sp' ou 'df_clientes_resto_br' não foram encontrados.")
    print("Por favor, execute novamente as células anteriores que criam esses perfis.")
    exit()

# --- 2. Selecionar as Features "Platinum" para a Clusterização Nacional ---
features_para_cluster_platinum = [
    'Frequencia', 'Monetaridade_Total', 'Ticket_Medio', 'Quantidade_Total', 
    'Pct_Compras_Com_Desconto', 'Pct_Compras_Canal_Otica', 'Pct_Compras_Loja_Rua',
    'Pct_Compras_Quiosque', 'Pct_Compras_Loja_Shopping', 'N_Lojas_Unicas', 
    'Recencia', 'Tempo_Como_Cliente', 'Idade', 'Itens_Por_Transacao', 
    'Preco_Medio_Item', 'Cadencia_Compra'
]

data_nacional_cluster = df_clientes_nacional[features_para_cluster_platinum].dropna()

# --- 3. Padronizar a Escala ---
scaler_nacional = StandardScaler()
data_nacional_scaled = scaler_nacional.fit_transform(data_nacional_cluster)

# --- 4. Aplicar o Método do Cotovelo na Base Nacional ---
wcss_nacional = []
print("\nCalculando o WCSS para 1 a 10 clusters em nível nacional...")
for i in range(1, 11):
    kmeans = KMeans(n_clusters=i, init='k-means++', random_state=42, n_init=10)
    kmeans.fit(data_nacional_scaled)
    wcss_nacional.append(kmeans.inertia_)
print("Cálculo concluído.")

# --- 5. Plotar o Gráfico do Cotovelo Nacional ---
plt.figure(figsize=(10, 6))
plt.plot(range(1, 11), wcss_nacional, marker='o', linestyle='--')
plt.title('Método do Cotovelo (Features Platinum) - Nível Nacional')
plt.xlabel('Número de Clusters')
plt.ylabel('WCSS (Inertia)')
plt.grid(True)
plt.show()

# CÉLULA 9

# Verificar e criar conjuntos de treino/teste se necessário
from sklearn.model_selection import train_test_split

# Verificar se os conjuntos de treino já existem
if 'X_train_treated' not in globals() or 'y_train' not in globals():
    print("Criando conjuntos de treino e teste...")
    
    # Verificar se temos o dataset nacional
    if 'df_clientes_nacional' in globals():
        # Preparar features e target
        print("Usando df_clientes_nacional...")
        
        # Definir target (y) - assumindo que queremos prever compra de grau
        y = df_clientes_nacional['Comprou_Grau_Historico']
        
        # Definir features (X) - remover ID e target
        columns_to_exclude = ['ID_Cliente', 'Comprou_Grau_Historico']
        X = df_clientes_nacional.drop(columns=columns_to_exclude, errors='ignore')
        
        # Tratar valores faltantes
        print("Tratando valores faltantes...")
        # Para features numéricas, preencher com mediana
        numeric_cols = X.select_dtypes(include=['int64', 'float64']).columns
        X[numeric_cols] = X[numeric_cols].fillna(X[numeric_cols].median())
        
        # Para features categóricas, preencher com moda ou valor especial
        categorical_cols = X.select_dtypes(include=['object']).columns
        for col in categorical_cols:
            X[col] = X[col].fillna('Unknown')
        
        # Para outras colunas (ex: Cluster), preencher com -1
        other_cols = X.columns.difference(numeric_cols.union(categorical_cols))
        for col in other_cols:
            X[col] = X[col].fillna(-1)
        
        print(f"Shape do dataset: X={X.shape}, y={y.shape}")
        print(f"Distribuição do target: {y.value_counts().to_dict()}")
        
        # Dividir em treino e teste
        X_train_treated, X_test_treated, y_train, y_test = train_test_split(
            X, y, test_size=0.2, random_state=42, stratify=y
        )
        
        print(f"Treino: X={X_train_treated.shape}, y={y_train.shape}")
        print(f"Teste: X={X_test_treated.shape}, y={y_test.shape}")
        
    else:
        print("ERRO: df_clientes_nacional não encontrado.")
        print("Execute primeiro as células de preparação dos dados.")
else:
    print("Conjuntos de treino já existem.")

# CÉLULA 10: MODELOS COM MÉTRICAS COMPLETAS E COMPARAÇÃO ANTES/DEPOIS

from sklearn.ensemble import GradientBoostingClassifier, RandomForestClassifier
from sklearn.linear_model import RidgeClassifier
from sklearn.pipeline import Pipeline
from sklearn.preprocessing import StandardScaler, OneHotEncoder
from sklearn.compose import ColumnTransformer
from sklearn.model_selection import RandomizedSearchCV, StratifiedKFold
from sklearn.metrics import (accuracy_score, precision_score, recall_score, f1_score, 
                           roc_auc_score, mean_squared_error, r2_score, 
                           classification_report, roc_curve, auc)
import numpy as np
import pandas as pd
from scipy.stats import randint, uniform
import warnings
warnings.filterwarnings('ignore')

def calculate_all_metrics(y_true, y_pred, y_pred_proba=None):
    """Calcula todas as métricas necessárias"""
    metrics = {}
    
    # Métricas básicas de classificação
    metrics['accuracy'] = accuracy_score(y_true, y_pred)
    metrics['precision'] = precision_score(y_true, y_pred, zero_division=0)
    metrics['recall'] = recall_score(y_true, y_pred, zero_division=0)
    metrics['f1'] = f1_score(y_true, y_pred, zero_division=0)
    
    # AUC (precisa de probabilidades)
    if y_pred_proba is not None:
        try:
            # Para classificação binária
            if len(np.unique(y_true)) == 2:
                if hasattr(y_pred_proba, 'shape') and len(y_pred_proba.shape) > 1:
                    # Se y_pred_proba é uma matriz 2D, pegar a coluna da classe positiva
                    metrics['auc'] = roc_auc_score(y_true, y_pred_proba[:, 1])
                else:
                    # Se y_pred_proba já é um vetor 1D
                    metrics['auc'] = roc_auc_score(y_true, y_pred_proba)
            else:
                # Para classificação multiclasse
                metrics['auc'] = roc_auc_score(y_true, y_pred_proba, multi_class='ovr')
        except:
            metrics['auc'] = 0.0
    else:
        metrics['auc'] = 0.0
    
    # MSE (Mean Squared Error) - converte predições para usar como regressão
    metrics['mse'] = mean_squared_error(y_true, y_pred)
    
    # R² Score
    try:
        metrics['r2'] = r2_score(y_true, y_pred)
    except:
        metrics['r2'] = 0.0
    
    return metrics

print("=== MODELOS COM MÉTRICAS COMPLETAS E OTIMIZAÇÃO DE HIPERPARÂMETROS ===")
print("Algoritmos: Gradient Boosting + Random Forest + Ridge Regression")
print("Métricas: F1, Accuracy, AUC, R², MSE, Precision, Recall")

# Verificar pré-requisitos
required_vars = ['X_train_treated', 'y_train', 'X_test_treated', 'y_test']
missing_vars = [var for var in required_vars if var not in globals()]

if missing_vars:
    print(f"ERRO: Variáveis não encontradas: {missing_vars}")
    print("Execute primeiro as células 1-9 que criam essas variáveis.")
else:
    print("✅ Todas as variáveis necessárias encontradas")
    
    # Verificar dados
    print(f"Shape dos dados: X_train={X_train_treated.shape}, y_train={y_train.shape}")
    print(f"Distribuição do target: {y_train.value_counts().to_dict()}")
    
    # Criar preprocessador
    numeric_features = X_train_treated.select_dtypes(include=[np.number]).columns.tolist()
    categorical_features = X_train_treated.select_dtypes(include=['object']).columns.tolist()
    
    print(f"Features numéricas: {len(numeric_features)}")
    print(f"Features categóricas: {len(categorical_features)}")
    
    transformers = []
    if numeric_features:
        transformers.append(('num', StandardScaler(), numeric_features))
    if categorical_features:
        transformers.append(('cat', OneHotEncoder(drop='first', handle_unknown='ignore'), categorical_features))
    
    if transformers:
        preprocessor = ColumnTransformer(transformers=transformers, remainder='passthrough')
    else:
        preprocessor = 'passthrough'
    
    # 1. CRIAR MODELOS BASELINE
    print("\n1. CRIANDO MODELOS BASELINE...")
    
    models_baseline = {
        'GradientBoosting': Pipeline([
            ('preprocessor', preprocessor),
            ('classifier', GradientBoostingClassifier(
                n_estimators=100, learning_rate=0.1, max_depth=3, random_state=42))
        ]),
        'RandomForest': Pipeline([
            ('preprocessor', preprocessor),
            ('classifier', RandomForestClassifier(
                n_estimators=100, max_depth=5, random_state=42))
        ]),
        'Ridge': Pipeline([
            ('preprocessor', preprocessor),
            ('classifier', RidgeClassifier(
                alpha=1.0, random_state=42))
        ])
    }
    
    baseline_results = {}
    
    for name, model in models_baseline.items():
        try:
            print(f"Treinando {name} baseline...")
            model.fit(X_train_treated, y_train)
            
            y_pred = model.predict(X_test_treated)
            
            # Obter probabilidades se possível
            y_pred_proba = None
            if hasattr(model.named_steps['classifier'], 'predict_proba'):
                try:
                    y_pred_proba = model.predict_proba(X_test_treated)
                except:
                    pass
            elif hasattr(model.named_steps['classifier'], 'decision_function'):
                try:
                    decision_scores = model.decision_function(X_test_treated)
                    # Converter decision function para probabilidades (aproximação)
                    from scipy.special import expit
                    y_pred_proba = expit(decision_scores)
                except:
                    pass
            
            # Calcular todas as métricas
            metrics = calculate_all_metrics(y_test, y_pred, y_pred_proba)
            
            baseline_results[name] = {
                'model': model,
                'predictions': y_pred,
                'probabilities': y_pred_proba,
                'metrics': metrics
            }
            
            print(f"  F1-Score: {metrics['f1']:.4f} | AUC: {metrics['auc']:.4f} | R²: {metrics['r2']:.4f}")
            
        except Exception as e:
            print(f"  Erro no {name}: {e}")
            baseline_results[name] = None
    
    # 2. DEFINIR ESPAÇOS DE HIPERPARÂMETROS
    print("\n2. DEFININDO ESPAÇOS DE HIPERPARÂMETROS...")
    
    param_distributions = {
        'GradientBoosting': {
            'classifier__n_estimators': randint(50, 300),
            'classifier__learning_rate': uniform(0.01, 0.29),
            'classifier__max_depth': randint(2, 8),
            'classifier__min_samples_split': randint(10, 100),
            'classifier__min_samples_leaf': randint(5, 50),
            'classifier__subsample': uniform(0.7, 0.29),
            'classifier__max_features': ['sqrt', 'log2', None]
        },
        'RandomForest': {
            'classifier__n_estimators': randint(50, 400),
            'classifier__max_depth': randint(3, 20),
            'classifier__min_samples_split': randint(2, 30),
            'classifier__min_samples_leaf': randint(1, 20),
            'classifier__max_features': ['sqrt', 'log2', None, 0.5, 0.8],
            'classifier__bootstrap': [True, False],
            'classifier__criterion': ['gini', 'entropy']
        },
        'Ridge': {
            'classifier__alpha': uniform(0.01, 50),
            'classifier__fit_intercept': [True, False],
            'classifier__solver': ['auto', 'svd', 'cholesky', 'lsqr', 'saga', 'sparse_cg'],
            'classifier__max_iter': randint(500, 2000)
        }
    }
    
    # 3. OTIMIZAÇÃO INDIVIDUAL DE CADA MODELO
    print("\n3. EXECUTANDO OTIMIZAÇÃO INDIVIDUAL...")
    
    cv_strategy = StratifiedKFold(n_splits=5, shuffle=True, random_state=42)
    optimized_models = {}
    
    # Usar amostra se dataset for muito grande
    if len(X_train_treated) > 10000:
        print(f"Dataset grande ({len(X_train_treated)} registros). Usando amostra de 10.000...")
        sample_indices = np.random.choice(len(X_train_treated), 10000, replace=False)
        X_sample = X_train_treated.iloc[sample_indices]
        y_sample = y_train.iloc[sample_indices]
    else:
        X_sample = X_train_treated
        y_sample = y_train
    
    for name in ['GradientBoosting', 'RandomForest', 'Ridge']:
        if baseline_results[name] is not None:
            print(f"\nOtimizando {name}...")
            
            try:
                # Criar pipeline base
                if name == 'GradientBoosting':
                    base_classifier = GradientBoostingClassifier(random_state=42)
                elif name == 'RandomForest':
                    base_classifier = RandomForestClassifier(random_state=42, n_jobs=-1)
                else:  # Ridge
                    base_classifier = RidgeClassifier(random_state=42)
                
                model_pipeline = Pipeline([
                    ('preprocessor', preprocessor),
                    ('classifier', base_classifier)
                ])
                
                # Configurar RandomizedSearch
                random_search = RandomizedSearchCV(
                    estimator=model_pipeline,
                    param_distributions=param_distributions[name],
                    n_iter=50 if name != 'Ridge' else 30,
                    cv=cv_strategy,
                    scoring='f1',
                    n_jobs=-1,
                    verbose=0,
                    random_state=42
                )
                
                # Executar busca
                print(f"  Executando {random_search.n_iter} iterações de otimização...")
                random_search.fit(X_sample, y_sample)
                
                # Treinar modelo final no dataset completo
                best_model = random_search.best_estimator_
                print(f"  Treinando modelo final no dataset completo...")
                best_model.fit(X_train_treated, y_train)
                
                # Avaliar
                y_pred_opt = best_model.predict(X_test_treated)
                
                # Obter probabilidades
                y_pred_proba_opt = None
                if hasattr(best_model.named_steps['classifier'], 'predict_proba'):
                    try:
                        y_pred_proba_opt = best_model.predict_proba(X_test_treated)
                    except:
                        pass
                elif hasattr(best_model.named_steps['classifier'], 'decision_function'):
                    try:
                        decision_scores = best_model.decision_function(X_test_treated)
                        from scipy.special import expit
                        y_pred_proba_opt = expit(decision_scores)
                    except:
                        pass
                
                # Calcular métricas otimizadas
                metrics_opt = calculate_all_metrics(y_test, y_pred_opt, y_pred_proba_opt)
                
                optimized_models[name] = {
                    'model': best_model,
                    'search_object': random_search,
                    'predictions': y_pred_opt,
                    'probabilities': y_pred_proba_opt,
                    'best_params': random_search.best_params_,
                    'best_score_cv': random_search.best_score_,
                    'metrics': metrics_opt
                }
                
                print(f"  Melhor CV F1: {random_search.best_score_:.4f}")
                print(f"  Teste - F1: {metrics_opt['f1']:.4f} | AUC: {metrics_opt['auc']:.4f} | R²: {metrics_opt['r2']:.4f}")
                
                # Calcular melhoria
                baseline_f1 = baseline_results[name]['metrics']['f1']
                improvement = ((metrics_opt['f1'] - baseline_f1) / baseline_f1 * 100) if baseline_f1 > 0 else 0
                print(f"  Melhoria F1: {improvement:+.1f}%")
                
            except Exception as e:
                print(f"  Erro na otimização do {name}: {e}")
                # Usar modelo baseline como fallback
                optimized_models[name] = baseline_results[name].copy()
                optimized_models[name]['best_params'] = {}
                optimized_models[name]['best_score_cv'] = baseline_results[name]['metrics']['f1']
    
    # 4. SELECIONAR MELHOR MODELO
    print("\n4. SELECIONANDO MELHOR MODELO...")
    
    try:
        # Encontrar o melhor modelo baseado no F1-Score
        best_model_name = max(optimized_models.items(), key=lambda x: x[1]['metrics']['f1'])[0]
        model_final = optimized_models[best_model_name]['model']
        final_metrics = optimized_models[best_model_name]['metrics']
        final_predictions = optimized_models[best_model_name]['predictions']
        final_probabilities = optimized_models[best_model_name]['probabilities']
        final_model_type = best_model_name
        
        print(f"✅ {best_model_name} selecionado como modelo final")
        print(f"F1-Score: {final_metrics['f1']:.4f}")
        
    except Exception as e:
        print(f"Erro na seleção do melhor modelo: {e}")
        # Fallback para primeiro modelo disponível
        available_models = [x for x in optimized_models.items() if x[1] is not None]
        if available_models:
            best_model_name = available_models[0][0]
            model_final = available_models[0][1]['model']
            final_metrics = available_models[0][1]['metrics']
            final_predictions = available_models[0][1]['predictions']
            final_probabilities = available_models[0][1]['probabilities']
            final_model_type = best_model_name
        else:
            print("ERRO: Nenhum modelo foi treinado com sucesso")
            final_model_type = None
    
    # 5. CRIAR TABELA COMPARATIVA DETALHADA
    if final_model_type is not None:
        print("\n5. TABELA COMPARATIVA - ANTES vs DEPOIS DOS HIPERPARÂMETROS:")
        print("=" * 120)
        
        # Criar DataFrame de comparação completa
        comparison_data = []
        
        for name in ['GradientBoosting', 'RandomForest', 'Ridge']:
            if baseline_results[name] is not None and optimized_models[name] is not None:
                baseline_metrics = baseline_results[name]['metrics']
                optimized_metrics = optimized_models[name]['metrics']
                
                comparison_data.append({
                    'Algoritmo': name,
                    'F1_Antes': baseline_metrics['f1'],
                    'F1_Depois': optimized_metrics['f1'],
                    'F1_Melhoria_%': ((optimized_metrics['f1'] - baseline_metrics['f1']) / baseline_metrics['f1'] * 100) if baseline_metrics['f1'] > 0 else 0,
                    'Accuracy_Antes': baseline_metrics['accuracy'],
                    'Accuracy_Depois': optimized_metrics['accuracy'],
                    'Accuracy_Melhoria_%': ((optimized_metrics['accuracy'] - baseline_metrics['accuracy']) / baseline_metrics['accuracy'] * 100) if baseline_metrics['accuracy'] > 0 else 0,
                    'AUC_Antes': baseline_metrics['auc'],
                    'AUC_Depois': optimized_metrics['auc'],
                    'AUC_Melhoria_%': ((optimized_metrics['auc'] - baseline_metrics['auc']) / baseline_metrics['auc'] * 100) if baseline_metrics['auc'] > 0 else 0,
                    'R2_Antes': baseline_metrics['r2'],
                    'R2_Depois': optimized_metrics['r2'],
                    'R2_Melhoria_%': ((optimized_metrics['r2'] - baseline_metrics['r2']) / abs(baseline_metrics['r2']) * 100) if baseline_metrics['r2'] != 0 else 0,
                    'MSE_Antes': baseline_metrics['mse'],
                    'MSE_Depois': optimized_metrics['mse'],
                    'MSE_Melhoria_%': ((baseline_metrics['mse'] - optimized_metrics['mse']) / baseline_metrics['mse'] * 100) if baseline_metrics['mse'] > 0 else 0,  # Menor é melhor
                    'Precision_Antes': baseline_metrics['precision'],
                    'Precision_Depois': optimized_metrics['precision'],
                    'Precision_Melhoria_%': ((optimized_metrics['precision'] - baseline_metrics['precision']) / baseline_metrics['precision'] * 100) if baseline_metrics['precision'] > 0 else 0,
                    'Recall_Antes': baseline_metrics['recall'],
                    'Recall_Depois': optimized_metrics['recall'],
                    'Recall_Melhoria_%': ((optimized_metrics['recall'] - baseline_metrics['recall']) / baseline_metrics['recall'] * 100) if baseline_metrics['recall'] > 0 else 0,
                })
        
        comparison_df = pd.DataFrame(comparison_data)
        
        # Mostrar tabela resumida
        print("\nTABELA RESUMIDA - PRINCIPAIS MÉTRICAS:")
        print("-" * 100)
        summary_cols = ['Algoritmo', 'F1_Antes', 'F1_Depois', 'F1_Melhoria_%', 
                       'AUC_Antes', 'AUC_Depois', 'AUC_Melhoria_%',
                       'Accuracy_Antes', 'Accuracy_Depois', 'Accuracy_Melhoria_%']
        print(comparison_df[summary_cols].to_string(index=False, float_format='%.4f'))
        
        print("\nTABELA DETALHADA - TODAS AS MÉTRICAS:")
        print("-" * 120)
        
        # Dividir em duas partes para melhor visualização
        part1_cols = ['Algoritmo', 'F1_Antes', 'F1_Depois', 'F1_Melhoria_%', 
                      'Precision_Antes', 'Precision_Depois', 'Precision_Melhoria_%',
                      'Recall_Antes', 'Recall_Depois', 'Recall_Melhoria_%']
        print("PARTE 1 - F1, Precision, Recall:")
        print(comparison_df[part1_cols].to_string(index=False, float_format='%.4f'))
        
        print("\nPARTE 2 - Accuracy, AUC, R², MSE:")
        part2_cols = ['Algoritmo', 'Accuracy_Antes', 'Accuracy_Depois', 'Accuracy_Melhoria_%',
                      'AUC_Antes', 'AUC_Depois', 'AUC_Melhoria_%',
                      'R2_Antes', 'R2_Depois', 'R2_Melhoria_%',
                      'MSE_Antes', 'MSE_Depois', 'MSE_Melhoria_%']
        print(comparison_df[part2_cols].to_string(index=False, float_format='%.4f'))
        
        # 6. ESTATÍSTICAS DE MELHORIA
        print("\n6. ESTATÍSTICAS DE MELHORIA:")
        print("-" * 60)
        
        metrics_names = ['F1', 'Accuracy', 'AUC', 'Precision', 'Recall', 'MSE', 'R2']
        improvement_stats = {}
        
        for metric in metrics_names:
            col_name = f'{metric}_Melhoria_%'
            if col_name in comparison_df.columns:
                improvements = comparison_df[col_name].values
                improvement_stats[metric] = {
                    'Media': np.mean(improvements),
                    'Mediana': np.median(improvements),
                    'Min': np.min(improvements),
                    'Max': np.max(improvements),
                    'Positivas': np.sum(improvements > 0),
                    'Total': len(improvements)
                }
        
        for metric, stats in improvement_stats.items():
            print(f"\n{metric}:")
            print(f"  Melhoria média: {stats['Media']:+.1f}%")
            print(f"  Faixa: {stats['Min']:+.1f}% a {stats['Max']:+.1f}%")
            print(f"  Algoritmos melhorados: {stats['Positivas']}/{stats['Total']}")
        
        # 7. SALVAR RESULTADOS COMPLETOS
        print("\n7. SALVANDO RESULTADOS...")
        
        hyperparameter_results = {
            'baseline_results': baseline_results,
            'optimized_models': optimized_models,
            'final_model_type': final_model_type,
            'final_metrics': final_metrics,
            'comparison_df': comparison_df,
            'improvement_stats': improvement_stats,
            'optimization_success': True
        }
        
        # Salvar predições para análise
        globals()['y_pred_optimized'] = final_predictions
        globals()['y_pred_proba_optimized'] = final_probabilities
        if baseline_results.get('GradientBoosting'):
            globals()['y_pred_baseline'] = baseline_results['GradientBoosting']['predictions']
        
        print(f"\n✅ PROCESSO CONCLUÍDO!")
        print(f"✅ Modelo final: {final_model_type}")
        print(f"✅ F1-Score final: {final_metrics['f1']:.4f}")
        print(f"✅ AUC final: {final_metrics['auc']:.4f}")
        print(f"✅ R² final: {final_metrics['r2']:.4f}")
        print(f"✅ model_final disponível")
        print(f"✅ hyperparameter_results disponível") 
        print(f"✅ comparison_df disponível")
        print(f"✅ Célula 10.1 pode ser executada")

# CÉLULA 10.1: ANÁLISE VISUAL E COMPARATIVA COMPLETA

import matplotlib.pyplot as plt
import seaborn as sns
from sklearn.metrics import classification_report, confusion_matrix, roc_curve, auc
import numpy as np
import pandas as pd

print("=== ANÁLISE VISUAL E COMPARATIVA COMPLETA ===\n")

if 'hyperparameter_results' not in globals():
    print("ERRO: hyperparameter_results não encontrado.")
    print("Execute primeiro a célula 10 modificada.")
else:
    results = hyperparameter_results
    
    print(f"Modelo final selecionado: {results['final_model_type']}")
    print(f"Métricas finais:")
    final_metrics = results['final_metrics']
    for metric, value in final_metrics.items():
        print(f"  {metric.upper()}: {value:.4f}")
    
    # 1. VISUALIZAÇÕES COMPARATIVAS EXTENSAS
    print(f"\n1. GERANDO VISUALIZAÇÕES COMPARATIVAS...")
    
    try:
        # Configurar o estilo
        plt.style.use('default')
        sns.set_palette("husl")
        
        # Criar figura com múltiplos subplots
        fig = plt.figure(figsize=(20, 15))
        
        # Layout: 3 linhas x 3 colunas = 9 subplots
        gs = fig.add_gridspec(3, 3, hspace=0.3, wspace=0.3)
        
        comparison_df = results['comparison_df']
        
        # 1.1 Comparação F1-Score
        ax1 = fig.add_subplot(gs[0, 0])
        models = comparison_df['Algoritmo']
        f1_antes = comparison_df['F1_Antes']
        f1_depois = comparison_df['F1_Depois']
        
        x = np.arange(len(models))
        width = 0.35
        
        bars1 = ax1.bar(x - width/2, f1_antes, width, label='Antes', alpha=0.8, color='lightcoral')
        bars2 = ax1.bar(x + width/2, f1_depois, width, label='Depois', alpha=0.8, color='lightblue')
        
        # Adicionar valores nas barras
        for i, (antes, depois) in enumerate(zip(f1_antes, f1_depois)):
            ax1.text(i - width/2, antes + 0.01, f'{antes:.3f}', ha='center', va='bottom', fontsize=9)
            ax1.text(i + width/2, depois + 0.01, f'{depois:.3f}', ha='center', va='bottom', fontsize=9)
        
        ax1.set_xlabel('Algoritmos')
        ax1.set_ylabel('F1-Score')
        ax1.set_title('F1-Score: Antes vs Depois')
        ax1.set_xticks(x)
        ax1.set_xticklabels(models, rotation=45, ha='right')
        ax1.legend()
        ax1.grid(True, alpha=0.3)
        
        # 1.2 Comparação AUC
        ax2 = fig.add_subplot(gs[0, 1])
        auc_antes = comparison_df['AUC_Antes']
        auc_depois = comparison_df['AUC_Depois']
        
        bars1 = ax2.bar(x - width/2, auc_antes, width, label='Antes', alpha=0.8, color='lightcoral')
        bars2 = ax2.bar(x + width/2, auc_depois, width, label='Depois', alpha=0.8, color='lightblue')
        
        for i, (antes, depois) in enumerate(zip(auc_antes, auc_depois)):
            ax2.text(i - width/2, antes + 0.01, f'{antes:.3f}', ha='center', va='bottom', fontsize=9)
            ax2.text(i + width/2, depois + 0.01, f'{depois:.3f}', ha='center', va='bottom', fontsize=9)
        
        ax2.set_xlabel('Algoritmos')
        ax2.set_ylabel('AUC')
        ax2.set_title('AUC: Antes vs Depois')
        ax2.set_xticks(x)
        ax2.set_xticklabels(models, rotation=45, ha='right')
        ax2.legend()
        ax2.grid(True, alpha=0.3)
        
        # 1.3 Comparação Accuracy
        ax3 = fig.add_subplot(gs[0, 2])
        acc_antes = comparison_df['Accuracy_Antes']
        acc_depois = comparison_df['Accuracy_Depois']
        
        bars1 = ax3.bar(x - width/2, acc_antes, width, label='Antes', alpha=0.8, color='lightcoral')
        bars2 = ax3.bar(x + width/2, acc_depois, width, label='Depois', alpha=0.8, color='lightblue')
        
        for i, (antes, depois) in enumerate(zip(acc_antes, acc_depois)):
            ax3.text(i - width/2, antes + 0.01, f'{antes:.3f}', ha='center', va='bottom', fontsize=9)
            ax3.text(i + width/2, depois + 0.01, f'{depois:.3f}', ha='center', va='bottom', fontsize=9)
        
        ax3.set_xlabel('Algoritmos')
        ax3.set_ylabel('Accuracy')
        ax3.set_title('Accuracy: Antes vs Depois')
        ax3.set_xticks(x)
        ax3.set_xticklabels(models, rotation=45, ha='right')
        ax3.legend()
        ax3.grid(True, alpha=0.3)
        
        # 1.4 Melhorias Percentuais - F1, AUC, Accuracy
        ax4 = fig.add_subplot(gs[1, 0])
        f1_improvements = comparison_df['F1_Melhoria_%']
        auc_improvements = comparison_df['AUC_Melhoria_%']
        acc_improvements = comparison_df['Accuracy_Melhoria_%']
        
        x_pos = np.arange(len(models))
        width = 0.25
        
        bars1 = ax4.bar(x_pos - width, f1_improvements, width, label='F1', alpha=0.8)
        bars2 = ax4.bar(x_pos, auc_improvements, width, label='AUC', alpha=0.8)
        bars3 = ax4.bar(x_pos + width, acc_improvements, width, label='Accuracy', alpha=0.8)
        
        ax4.set_xlabel('Algoritmos')
        ax4.set_ylabel('Melhoria (%)')
        ax4.set_title('Comparação de Melhorias por Métrica')
        ax4.set_xticks(x_pos)
        ax4.set_xticklabels(models, rotation=45, ha='right')
        ax4.legend()
        ax4.grid(True, alpha=0.3)
        ax4.axhline(y=0, color='black', linestyle='-', alpha=0.5)
        
        # 1.5 Precision vs Recall (Antes)
        ax5 = fig.add_subplot(gs[1, 1])
        precision_antes = comparison_df['Precision_Antes']
        recall_antes = comparison_df['Recall_Antes']
        
        scatter = ax5.scatter(recall_antes, precision_antes, s=100, alpha=0.7, c='lightcoral', label='Antes')
        for i, model in enumerate(models):
            ax5.annotate(f'{model}\n({precision_antes[i]:.3f}, {recall_antes[i]:.3f})', 
                        (recall_antes[i], precision_antes[i]), 
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        ax5.set_xlabel('Recall')
        ax5.set_ylabel('Precision')
        ax5.set_title('Precision vs Recall (ANTES)')
        ax5.grid(True, alpha=0.3)
        ax5.legend()
        
        # 1.6 Precision vs Recall (Depois)
        ax6 = fig.add_subplot(gs[1, 2])
        precision_depois = comparison_df['Precision_Depois']
        recall_depois = comparison_df['Recall_Depois']
        
        scatter = ax6.scatter(recall_depois, precision_depois, s=100, alpha=0.7, c='lightblue', label='Depois')
        for i, model in enumerate(models):
            ax6.annotate(f'{model}\n({precision_depois[i]:.3f}, {recall_depois[i]:.3f})', 
                        (recall_depois[i], precision_depois[i]), 
                        xytext=(5, 5), textcoords='offset points', fontsize=8)
        
        ax6.set_xlabel('Recall')
        ax6.set_ylabel('Precision')
        ax6.set_title('Precision vs Recall (DEPOIS)')
        ax6.grid(True, alpha=0.3)
        ax6.legend()
        
        # 1.7 Heatmap de Melhorias
        ax7 = fig.add_subplot(gs[2, 0])
        metrics_improvement = comparison_df[['F1_Melhoria_%', 'Accuracy_Melhoria_%', 'AUC_Melhoria_%', 
                                           'Precision_Melhoria_%', 'Recall_Melhoria_%', 'R2_Melhoria_%']].values
        metrics_names = ['F1', 'Accuracy', 'AUC', 'Precision', 'Recall', 'R²']
        
        im = ax7.imshow(metrics_improvement.T, cmap='RdYlGn', aspect='auto')
        ax7.set_xticks(range(len(models)))
        ax7.set_xticklabels(models, rotation=45, ha='right')
        ax7.set_yticks(range(len(metrics_names)))
        ax7.set_yticklabels(metrics_names)
        ax7.set_title('Heatmap de Melhorias (%)')
        
        # Adicionar valores no heatmap
        for i in range(len(models)):
            for j in range(len(metrics_names)):
                text = ax7.text(i, j, f'{metrics_improvement[i, j]:.1f}%',
                               ha="center", va="center", color="black", fontsize=8)
        
        plt.colorbar(im, ax=ax7)
        
        # 1.8 Matriz de Confusão do Modelo Final
        if 'y_pred_optimized' in globals() and 'y_test' in globals():
            ax8 = fig.add_subplot(gs[2, 1])
            cm = confusion_matrix(y_test, y_pred_optimized)
            sns.heatmap(cm, annot=True, fmt='d', cmap='Blues', ax=ax8, cbar_kws={'shrink': 0.8})
            ax8.set_title(f'Matriz Confusão - {results["final_model_type"]}')
            ax8.set_xlabel('Predito')
            ax8.set_ylabel('Real')
        
        # 1.9 Curva ROC do Modelo Final
        if ('y_pred_proba_optimized' in globals() and 
            y_pred_proba_optimized is not None and 
            'y_test' in globals()):
            ax9 = fig.add_subplot(gs[2, 2])
            
            try:
                # Verificar se é binário
                if len(np.unique(y_test)) == 2:
                    if hasattr(y_pred_proba_optimized, 'shape') and len(y_pred_proba_optimized.shape) > 1:
                        proba_pos = y_pred_proba_optimized[:, 1]
                    else:
                        proba_pos = y_pred_proba_optimized
                    
                    fpr, tpr, _ = roc_curve(y_test, proba_pos)
                    roc_auc = auc(fpr, tpr)
                    
                    ax9.plot(fpr, tpr, color='darkorange', lw=2, 
                             label=f'{results["final_model_type"]} (AUC = {roc_auc:.3f})')
                    ax9.plot([0, 1], [0, 1], color='navy', lw=2, linestyle='--', alpha=0.8)
                    ax9.set_xlim([0.0, 1.0])
                    ax9.set_ylim([0.0, 1.05])
                    ax9.set_xlabel('Taxa Falso Positivo')
                    ax9.set_ylabel('Taxa Verdadeiro Positivo')
                    ax9.set_title(f'Curva ROC - {results["final_model_type"]}')
                    ax9.legend(loc="lower right")
                    ax9.grid(True, alpha=0.3)
                else:
                    ax9.text(0.5, 0.5, 'Curva ROC\nnão disponível\n(multiclasse)', 
                             ha='center', va='center', transform=ax9.transAxes)
            except Exception as e:
                ax9.text(0.5, 0.5, f'Erro na Curva ROC:\n{str(e)[:50]}', 
                         ha='center', va='center', transform=ax9.transAxes)
        else:
            ax9 = fig.add_subplot(gs[2, 2])
            # Ranking Final dos Modelos
            final_f1_scores = comparison_df['F1_Depois'].values
            sorted_indices = np.argsort(final_f1_scores)[::-1]  # Decrescente
            
            ranked_models = [models.iloc[i] for i in sorted_indices]
            ranked_scores = [final_f1_scores[i] for i in sorted_indices]
            
            bars = ax9.barh(range(len(ranked_models)), ranked_scores, alpha=0.7)
            
            # Colorir o melhor modelo
            bars[0].set_color('gold')
            bars[0].set_edgecolor('orange')
            bars[0].set_linewidth(2)
            
            ax9.set_yticks(range(len(ranked_models)))
            ax9.set_yticklabels([f"#{i+1}. {model}" for i, model in enumerate(ranked_models)])
            ax9.set_xlabel('F1-Score Final')
            ax9.set_title('Ranking Final dos Modelos')
            ax9.grid(True, alpha=0.3, axis='x')
            
            # Adicionar valores nas barras
            for i, score in enumerate(ranked_scores):
                ax9.text(score + 0.01, i, f'{score:.3f}', ha='left', va='center', fontsize=9)
        
        plt.tight_layout()
        plt.show()
        
    except Exception as e:
        print(f"Erro ao criar visualizações: {e}")
        import traceback
        traceback.print_exc()
    
    # 2. TABELA COMPARATIVA COMPLETA - TODOS OS ALGORITMOS E MÉTRICAS
    print(f"\n2. TABELA COMPARATIVA COMPLETA - ANTES vs DEPOIS:")
    print("=" * 150)
    
    # Criar tabela detalhada para todos os algoritmos
    detailed_table_data = []
    
    for algo_name in ['GradientBoosting', 'RandomForest', 'Ridge']:
        if (algo_name in baseline_results and baseline_results[algo_name] is not None and
            algo_name in optimized_models and optimized_models[algo_name] is not None):
            
            baseline_metrics = baseline_results[algo_name]['metrics']
            optimized_metrics = optimized_models[algo_name]['metrics']
            
            # Adicionar linha para cada métrica
            for metric in ['f1', 'accuracy', 'auc', 'precision', 'recall', 'r2', 'mse']:
                antes = baseline_metrics[metric]
                depois = optimized_metrics[metric]
                
                # Calcular melhoria
                if antes != 0:
                    if metric == 'mse':  # Para MSE, menor é melhor
                        melhoria = (antes - depois) / antes * 100
                    else:
                        melhoria = (depois - antes) / abs(antes) * 100
                else:
                    melhoria = 0
                
                status = "✅" if melhoria > 1 else "❌" if melhoria < -1 else "➖"
                
                detailed_table_data.append({
                    'Algoritmo': algo_name,
                    'Metrica': metric.upper(),
                    'Antes': antes,
                    'Depois': depois,
                    'Melhoria_%': melhoria,
                    'Status': status
                })
    
    detailed_df = pd.DataFrame(detailed_table_data)
    
    # Mostrar tabela por algoritmo
    for algo in ['GradientBoosting', 'RandomForest', 'Ridge']:
        algo_data = detailed_df[detailed_df['Algoritmo'] == algo]
        if not algo_data.empty:
            print(f"\n{algo.upper()}")
            print("-" * 80)
            print(algo_data[['Metrica', 'Antes', 'Depois', 'Melhoria_%', 'Status']].to_string(
                index=False, float_format='%.4f'))
    
    # Tabela consolidada com todas as métricas lado a lado
    print(f"\n\nTABELA CONSOLIDADA - RESUMO GERAL:")
    print("=" * 150)
    
    # Reorganizar dados para mostrar métricas como colunas
    consolidated_data = []
    for algo_name in ['GradientBoosting', 'RandomForest', 'Ridge']:
        if (algo_name in baseline_results and baseline_results[algo_name] is not None and
            algo_name in optimized_models and optimized_models[algo_name] is not None):
            
            baseline_metrics = baseline_results[algo_name]['metrics']
            optimized_metrics = optimized_models[algo_name]['metrics']
            
            row = {'Algoritmo': algo_name}
            
            # Adicionar métricas antes
            for metric in ['f1', 'accuracy', 'auc', 'precision', 'recall', 'r2', 'mse']:
                row[f'{metric.upper()}_Antes'] = baseline_metrics[metric]
                row[f'{metric.upper()}_Depois'] = optimized_metrics[metric]
                
                # Calcular melhoria
                antes = baseline_metrics[metric]
                depois = optimized_metrics[metric]
                if antes != 0:
                    if metric == 'mse':
                        melhoria = (antes - depois) / antes * 100
                    else:
                        melhoria = (depois - antes) / abs(antes) * 100
                else:
                    melhoria = 0
                row[f'{metric.upper()}_Melhoria_%'] = melhoria
            
            consolidated_data.append(row)
    
    consolidated_df = pd.DataFrame(consolidated_data)
    
    # Mostrar por grupos de métricas para melhor legibilidade
    print("\nGRUPO 1 - MÉTRICAS PRINCIPAIS:")
    print("-" * 100)
    grupo1_cols = ['Algoritmo', 'F1_Antes', 'F1_Depois', 'F1_Melhoria_%', 
                   'ACCURACY_Antes', 'ACCURACY_Depois', 'ACCURACY_Melhoria_%',
                   'AUC_Antes', 'AUC_Depois', 'AUC_Melhoria_%']
    print(consolidated_df[grupo1_cols].to_string(index=False, float_format='%.4f'))
    
    print("\nGRUPO 2 - PRECISION, RECALL, R²:")
    print("-" * 100)
    grupo2_cols = ['Algoritmo', 'PRECISION_Antes', 'PRECISION_Depois', 'PRECISION_Melhoria_%',
                   'RECALL_Antes', 'RECALL_Depois', 'RECALL_Melhoria_%',
                   'R2_Antes', 'R2_Depois', 'R2_Melhoria_%']
    print(consolidated_df[grupo2_cols].to_string(index=False, float_format='%.4f'))
    
    print("\nGRUPO 3 - MSE:")
    print("-" * 60)
    grupo3_cols = ['Algoritmo', 'MSE_Antes', 'MSE_Depois', 'MSE_Melhoria_%']
    print(consolidated_df[grupo3_cols].to_string(index=False, float_format='%.4f'))
    
    # 3. ANÁLISE INDIVIDUAL DETALHADA POR ALGORITMO
    print(f"\n\n3. ANÁLISE INDIVIDUAL DETALHADA:")
    print("=" * 80)
    
    optimized_models = results['optimized_models']
    baseline_results = results['baseline_results']
    
    for algo_name in ['GradientBoosting', 'RandomForest', 'Ridge']:
        if (algo_name in baseline_results and baseline_results[algo_name] is not None and
            algo_name in optimized_models and optimized_models[algo_name] is not None):
            
            print(f"\n{algo_name.upper()}")
            print("-" * 60)
            
            baseline_metrics = baseline_results[algo_name]['metrics']
            optimized_metrics = optimized_models[algo_name]['metrics']
            
            # Mostrar melhores parâmetros
            print(f"MELHORES HIPERPARÂMETROS:")
            best_params = optimized_models[algo_name].get('best_params', {})
            for param, value in best_params.items():
                param_name = param.split('__')[1] if '__' in param else param
                print(f"  {param_name}: {value}")
            
            # CV Score
            cv_score = optimized_models[algo_name].get('best_score_cv', 0)
            print(f"\nMelhor CV F1-Score: {cv_score:.4f}")
            
            # Resumo de melhorias
            print(f"\nRESUMO DE MELHORIAS:")
            melhorias_positivas = 0
            total_metricas = 0
            
            for metric in ['f1', 'accuracy', 'auc', 'precision', 'recall', 'r2']:
                antes = baseline_metrics[metric]
                depois = optimized_metrics[metric]
                if antes != 0:
                    melhoria = (depois - antes) / abs(antes) * 100
                    total_metricas += 1
                    if melhoria > 0:
                        melhorias_positivas += 1
            
            # MSE (menor é melhor)
            mse_antes = baseline_metrics['mse']
            mse_depois = optimized_metrics['mse']
            if mse_antes > 0:
                mse_melhoria = (mse_antes - mse_depois) / mse_antes * 100
                total_metricas += 1
                if mse_melhoria > 0:
                    melhorias_positivas += 1
            
            taxa_melhoria = (melhorias_positivas / total_metricas * 100) if total_metricas > 0 else 0
            print(f"  Métricas melhoradas: {melhorias_positivas}/{total_metricas} ({taxa_melhoria:.1f}%)")
            
            # Performance geral
            if taxa_melhoria >= 80:
                performance = "EXCELENTE"
            elif taxa_melhoria >= 60:
                performance = "BOA"
            elif taxa_melhoria >= 40:
                performance = "MODERADA"
            else:
                performance = "LIMITADA"
            
            print(f"  Performance da otimização: {performance}")
        
        else:
            print(f"\n{algo_name.upper()}")
            print("-" * 60)
            print("  ❌ Algoritmo não foi treinado com sucesso")
    
    # 4. ESTATÍSTICAS GLOBAIS DE MELHORIA
    print(f"\n3. ESTATÍSTICAS GLOBAIS DE MELHORIA:")
    print("=" * 80)
    
    improvement_stats = results['improvement_stats']
    
    # Criar tabela de estatísticas
    stats_data = []
    for metric, stats in improvement_stats.items():
        stats_data.append({
            'Métrica': metric,
            'Melhoria_Média_%': stats['Media'],
            'Melhoria_Mediana_%': stats['Mediana'],
            'Melhor_Melhoria_%': stats['Max'],
            'Pior_Melhoria_%': stats['Min'],
            'Algoritmos_Melhorados': f"{stats['Positivas']}/{stats['Total']}"
        })
    
    stats_df = pd.DataFrame(stats_data)
    print(stats_df.to_string(index=False, float_format='%.2f'))
    
    # Resumo geral
    print(f"\nRESUMO GERAL:")
    avg_f1_improvement = improvement_stats['F1']['Media']
    print(f"  Melhoria média F1: {avg_f1_improvement:+.1f}%")
    
    if avg_f1_improvement > 5:
        status_geral = "EXCELENTE"
    elif avg_f1_improvement > 2:
        status_geral = "BOA"
    elif avg_f1_improvement > 0:
        status_geral = "MODERADA"
    else:
        status_geral = "LIMITADA"
    
    print(f"  Status da otimização: {status_geral}")
    
    # Contar algoritmos que melhoraram
    total_melhorias = sum(stats['Positivas'] for stats in improvement_stats.values())
    total_tentativas = sum(stats['Total'] for stats in improvement_stats.values())
    taxa_sucesso = (total_melhorias / total_tentativas * 100) if total_tentativas > 0 else 0
    
    print(f"  Taxa de sucesso geral: {taxa_sucesso:.1f}%")
    
    # 5. RELATÓRIO EXECUTIVO FINAL
    print(f"\n4. RELATÓRIO EXECUTIVO FINAL:")
    print("=" * 80)
    
    final_model = results['final_model_type']
    final_metrics = results['final_metrics']
    
    print(f"MODELO FINAL SELECIONADO: {final_model}")
    print(f"PERFORMANCE FINAL:")
    print(f"  • F1-Score: {final_metrics['f1']:.4f}")
    print(f"  • Accuracy: {final_metrics['accuracy']:.4f}")  
    print(f"  • AUC: {final_metrics['auc']:.4f}")
    print(f"  • Precision: {final_metrics['precision']:.4f}")
    print(f"  • Recall: {final_metrics['recall']:.4f}")
    print(f"  • R² Score: {final_metrics['r2']:.4f}")
    print(f"  • MSE: {final_metrics['mse']:.4f}")
    
    # Classificação da performance
    f1_final = final_metrics['f1']
    if f1_final >= 0.90:
        performance_class = "EXCEPCIONAL"
        recommendation = "✅ DEPLOY IMEDIATO RECOMENDADO"
    elif f1_final >= 0.85:
        performance_class = "EXCELENTE"
        recommendation = "✅ DEPLOY RECOMENDADO"
    elif f1_final >= 0.80:
        performance_class = "BOA"
        recommendation = "✅ DEPLOY APROVADO com monitoramento"
    elif f1_final >= 0.75:
        performance_class = "ADEQUADA"
        recommendation = "⚠️ DEPLOY CONDICIONAL - considere melhorias"
    else:
        performance_class = "LIMITADA"
        recommendation = "❌ REQUER MELHORIAS antes do deploy"
    
    print(f"\nCLASSIFICAÇÃO: {performance_class}")
    print(f"RECOMENDAÇÃO: {recommendation}")
    
    # Próximos passos
    print(f"\nPRÓXIMOS PASSOS RECOMENDADOS:")
    print(f"1. Validação com dados holdout/produção")
    print(f"2. Análise de interpretabilidade do modelo")
    print(f"3. Configuração de monitoramento de drift")
    print(f"4. Definição de thresholds de negócio")
    print(f"5. Testes A/B em ambiente controlado")
    
    # Alertas específicos
    if final_metrics['auc'] < 0.7:
        print(f"\n⚠️ ALERTA: AUC baixo ({final_metrics['auc']:.3f}) - verificar balanceamento das classes")
    
    if abs(final_metrics['r2']) < 0.1:
        print(f"\n⚠️ ALERTA: R² próximo de zero ({final_metrics['r2']:.3f}) - verificar adequação do modelo")
    
    precision_recall_diff = abs(final_metrics['precision'] - final_metrics['recall'])
    if precision_recall_diff > 0.2:
        print(f"\n⚠️ ALERTA: Grande diferença entre Precision ({final_metrics['precision']:.3f}) e Recall ({final_metrics['recall']:.3f})")
    
    print(f"\n=== ANÁLISE COMPLETA FINALIZADA ===")
    print(f"✅ Visualizações geradas: 9 gráficos comparativos")
    print(f"✅ Análise detalhada: 3 algoritmos individuais")
    print(f"✅ Métricas completas: 7 métricas por modelo")
    print(f"✅ Comparação antes/depois: 100% dos modelos")
    print(f"✅ Relatório executivo: Completo")
    print(f"✅ Status: Pronto para tomada de decisão")

# CÉLULA 11

# Importar métrica de avaliação
from sklearn.metrics import accuracy_score

# Treinar o modelo com os dados tratados
print('Treinando model_final (GradientBoosting) em X_train_treated / y_train...')
model_final.fit(X_train_treated, y_train)
print('Treinamento concluído.')

# Avaliação rápida no conjunto de teste
print('\nAvaliação no conjunto de teste:')
y_pred = model_final.predict(X_test_treated)
print(f'Acurácia: {accuracy_score(y_test, y_pred):.4f}')

# CÉLULA 12

# Análise detalhada do modelo
from sklearn.metrics import classification_report, confusion_matrix
import seaborn as sns

# 1. Relatório de classificação detalhado
print("Relatório de Classificação:")
print(classification_report(y_test, y_pred))

# 2. Matriz de Confusão
plt.figure(figsize=(10, 8))
cm = confusion_matrix(y_test, y_pred)
sns.heatmap(cm, annot=True, fmt='d', cmap='Blues')
plt.title('Matriz de Confusão')
plt.ylabel('Real')
plt.xlabel('Previsto')
plt.show()

# 3. Importância das Features - VERSÃO SIMPLIFICADA E ROBUSTA
print("\n" + "="*50)
print("ANÁLISE DE IMPORTÂNCIA DAS FEATURES")
print("="*50)

try:
    # Obter importâncias do modelo
    feature_importances = model_final.named_steps['classifier'].feature_importances_
    print(f"Número total de features no modelo: {len(feature_importances)}")
    
    # Tentar obter nomes das features do preprocessor já ajustado
    feature_names = []
    
    try:
        # Método 1: Usar get_feature_names_out do preprocessor ajustado
        feature_names = model_final.named_steps['preprocessor'].get_feature_names_out()
        print(f"Nomes das features obtidos do preprocessor: {len(feature_names)}")
    except:
        print("Não foi possível obter nomes das features do preprocessor.")
        
        # Método 2: Reconstruir nomes baseado nas features originais
        print("Reconstruindo nomes das features...")
        
        # Features numéricas (mantêm nome original após StandardScaler)
        numeric_features = X_train_treated.select_dtypes(include=[np.number]).columns.tolist()
        categorical_features = X_train_treated.select_dtypes(include=['object']).columns.tolist()
        
        # Adicionar features numéricas
        feature_names.extend(numeric_features)
        
        # Para features categóricas (OneHotEncoder cria múltiplas colunas)
        for cat_feature in categorical_features:
            unique_values = X_train_treated[cat_feature].unique()
            # OneHotEncoder com drop='first' remove a primeira categoria
            unique_values = sorted([val for val in unique_values if val != 'Unknown'])
            if len(unique_values) > 1:
                feature_names.extend([f"{cat_feature}_{val}" for val in unique_values[1:]])
            else:
                feature_names.append(f"{cat_feature}_encoded")
    
    # Ajustar se necessário
    if len(feature_names) != len(feature_importances):
        print(f"Aviso: Ajustando {len(feature_names)} nomes para {len(feature_importances)} importâncias")
        if len(feature_names) < len(feature_importances):
            # Adicionar nomes genéricos para features faltantes
            for i in range(len(feature_names), len(feature_importances)):
                feature_names.append(f"feature_extra_{i}")
        else:
            # Truncar se temos nomes demais
            feature_names = feature_names[:len(feature_importances)]
    
    # Criar DataFrame com importâncias
    feature_imp = pd.DataFrame({
        'feature': feature_names,
        'importance': feature_importances
    }).sort_values('importance', ascending=False)
    
    print(f"\nTop 15 Features Mais Importantes:")
    print("-" * 60)
    for i, (idx, row) in enumerate(feature_imp.head(15).iterrows(), 1):
        print(f"{i:2d}. {row['feature']:<35} {row['importance']:.4f}")
    
    # Visualizar as top 10 features mais importantes
    plt.figure(figsize=(14, 8))
    top_10 = feature_imp.head(10)
    sns.barplot(data=top_10, x='importance', y='feature', palette='viridis')
    plt.title('Top 10 Features Mais Importantes para Predição de Compra de Grau', fontsize=14)
    plt.xlabel('Importância Relativa', fontsize=12)
    plt.ylabel('Features', fontsize=12)
    
    # Adicionar valores nas barras
    for i, (idx, row) in enumerate(top_10.iterrows()):
        plt.text(row['importance'] + 0.001, i, f'{row["importance"]:.3f}', 
                va='center', fontsize=10)
    
    plt.tight_layout()
    plt.show()
    
    # Estatísticas das importâncias
    print(f"\nEstatísticas das Importâncias:")
    print(f"- Soma total: {feature_importances.sum():.4f}")
    print(f"- Top 5 features representam: {feature_imp.head(5)['importance'].sum():.1%} da importância total")
    print(f"- Top 10 features representam: {feature_imp.head(10)['importance'].sum():.1%} da importância total")
    
except Exception as e:
    print(f"Erro na análise de importância: {e}")
    print("\nUsando método de fallback...")
    
    # Fallback: análise básica sem nomes de features
    try:
        importances = model_final.named_steps['classifier'].feature_importances_
        indices = np.argsort(importances)[::-1]
        
        print(f"Top 10 Features por Índice:")
        for i in range(min(10, len(importances))):
            print(f"{i+1:2d}. Feature {indices[i]:3d}: {importances[indices[i]]:.4f}")
            
    except Exception as e2:
        print(f"Erro também no método de fallback: {e2}")
        print("Modelo pode não ter feature_importances_ (ex: se for SVM linear)")

# CÉLULA 13

# Análise de Propensão à Compra de Grau por Cluster

# Calcular média de compra de grau por cluster
propensao_grau = df_clientes_resto_br.groupby('Cluster')['Comprou_Grau_Historico'].mean().sort_values(ascending=False)

# Criar DataFrame com características principais por cluster
metricas_principais = [
    'Comprou_Grau_Historico', 'Monetaridade_Total', 'Ticket_Medio',
    'Frequencia', 'Pct_Compras_Canal_Otica', 'Pct_Compras_Loja_Shopping',
    'Pct_Compras_Quiosque', 'Pct_Compras_Loja_Rua', 'Recencia',
    'Idade', 'Tempo_Como_Cliente'
]

perfil_clusters = df_clientes_resto_br.groupby('Cluster')[metricas_principais].mean()
perfil_clusters['Tamanho_Cluster'] = df_clientes_resto_br.groupby('Cluster').size()
perfil_clusters['Pct_Cluster'] = (perfil_clusters['Tamanho_Cluster'] / len(df_clientes_resto_br) * 100).round(1)

# Ordenar clusters por propensão à compra de grau
perfil_clusters_ordenado = perfil_clusters.loc[propensao_grau.index]

# Exibir resultados
print("Propensão à Compra de Grau por Cluster (ordenado do maior para o menor):\n")
for cluster in propensao_grau.index:
    print(f"\n=== Cluster {cluster} ===")
    print(f"Propensão à Compra de Grau: {perfil_clusters_ordenado.loc[cluster, 'Comprou_Grau_Historico']:.1%}")
    print(f"Tamanho do Cluster: {perfil_clusters_ordenado.loc[cluster, 'Tamanho_Cluster']:.0f} clientes ({perfil_clusters_ordenado.loc[cluster, 'Pct_Cluster']:.1f}%)")
    print("\nCaracterísticas Principais:")
    print(f"- Monetaridade Total Média: R${perfil_clusters_ordenado.loc[cluster, 'Monetaridade_Total']:.2f}")
    print(f"- Ticket Médio: R${perfil_clusters_ordenado.loc[cluster, 'Ticket_Medio']:.2f}")
    print(f"- Frequência Média: {perfil_clusters_ordenado.loc[cluster, 'Frequencia']:.1f} compras")
    print("\nDistribuição de Canais:")
    print(f"- Óticas: {perfil_clusters_ordenado.loc[cluster, 'Pct_Compras_Canal_Otica']:.1%}")
    print(f"- Shopping: {perfil_clusters_ordenado.loc[cluster, 'Pct_Compras_Loja_Shopping']:.1%}")
    print(f"- Quiosque: {perfil_clusters_ordenado.loc[cluster, 'Pct_Compras_Quiosque']:.1%}")
    print(f"- Loja de Rua: {perfil_clusters_ordenado.loc[cluster, 'Pct_Compras_Loja_Rua']:.1%}")
    print("\nPerfil do Cliente:")
    print(f"- Idade Média: {perfil_clusters_ordenado.loc[cluster, 'Idade']:.1f} anos")
    print(f"- Tempo como Cliente: {perfil_clusters_ordenado.loc[cluster, 'Tempo_Como_Cliente']:.1f} meses")
    print(f"- Recência: {perfil_clusters_ordenado.loc[cluster, 'Recencia']:.1f} meses")

# CÉLULA 14

# Visualizações Comparativas dos Clusters

# Criar DataFrame com métricas principais
clusters_df = pd.DataFrame({
    'Cluster': propensao_grau.index,
    'Propensão_Grau': propensao_grau.values,
    'Tamanho': perfil_clusters_ordenado['Tamanho_Cluster'],
    'Monetaridade': perfil_clusters_ordenado['Monetaridade_Total'],
    'Ticket_Medio': perfil_clusters_ordenado['Ticket_Medio'],
    'Pct_Otica': perfil_clusters_ordenado['Pct_Compras_Canal_Otica']
})

# Configurar estilo dos gráficos
plt.style.use('default')
fig, axes = plt.subplots(2, 2, figsize=(15, 12))
fig.suptitle('Comparação dos Clusters - Propensão à Compra de Grau', fontsize=16, y=1.02)

# 1. Propensão vs Tamanho do Cluster
ax = axes[0,0]
scatter = ax.scatter(clusters_df['Tamanho'], clusters_df['Propensão_Grau'] * 100,
                    s=clusters_df['Monetaridade']/30, alpha=0.6)
for i, row in clusters_df.iterrows():
    ax.annotate(f"Cluster {int(row['Cluster'])}",
                (row['Tamanho'], row['Propensão_Grau'] * 100),
                xytext=(5, 5), textcoords='offset points')
ax.set_title('Propensão vs Tamanho do Cluster')
ax.set_xlabel('Número de Clientes')
ax.set_ylabel('Propensão à Compra de Grau (%)')
ax.grid(True, alpha=0.3)

# 2. Propensão vs Ticket Médio
ax = axes[0,1]
scatter = ax.scatter(clusters_df['Ticket_Medio'], clusters_df['Propensão_Grau'] * 100,
                    s=clusters_df['Tamanho']/30, alpha=0.6)
for i, row in clusters_df.iterrows():
    ax.annotate(f"Cluster {int(row['Cluster'])}",
                (row['Ticket_Medio'], row['Propensão_Grau'] * 100),
                xytext=(5, 5), textcoords='offset points')
ax.set_title('Propensão vs Ticket Médio')
ax.set_xlabel('Ticket Médio (R$)')
ax.set_ylabel('Propensão à Compra de Grau (%)')
ax.grid(True, alpha=0.3)

# 3. Propensão vs % Ótica
ax = axes[1,0]
scatter = ax.scatter(clusters_df['Pct_Otica'] * 100, clusters_df['Propensão_Grau'] * 100,
                    s=clusters_df['Tamanho']/30, alpha=0.6)
for i, row in clusters_df.iterrows():
    ax.annotate(f"Cluster {int(row['Cluster'])}",
                (row['Pct_Otica'] * 100, row['Propensão_Grau'] * 100),
                xytext=(5, 5), textcoords='offset points')
ax.set_title('Propensão vs % Compras em Ótica')
ax.set_xlabel('% Compras em Ótica')
ax.set_ylabel('Propensão à Compra de Grau (%)')
ax.grid(True, alpha=0.3)

# 4. Barplot de Propensão
ax = axes[1,1]
bars = ax.bar(clusters_df['Cluster'].astype(int), clusters_df['Propensão_Grau'] * 100)
ax.set_title('Propensão à Compra de Grau por Cluster')
ax.set_xlabel('Cluster')
ax.set_ylabel('Propensão (%)')
for bar in bars:
    height = bar.get_height()
    ax.text(bar.get_x() + bar.get_width()/2., height,
            f'{height:.1f}%', ha='center', va='bottom')
ax.grid(True, alpha=0.3)

plt.tight_layout()
plt.show()

# Correlações
print("\nCorrelações com Propensão à Compra de Grau:")
correlacoes = pd.DataFrame({
    'Métrica': [
        'Ticket Médio',
        '% Compras em Ótica',
        'Monetaridade Total',
        'Frequência'
    ],
    'Correlação': [
        clusters_df['Ticket_Medio'].corr(clusters_df['Propensão_Grau']),
        clusters_df['Pct_Otica'].corr(clusters_df['Propensão_Grau']),
        clusters_df['Monetaridade'].corr(clusters_df['Propensão_Grau']),
        perfil_clusters_ordenado['Frequencia'].corr(perfil_clusters_ordenado['Comprou_Grau_Historico'])
    ]
}).sort_values('Correlação', ascending=False)

print(correlacoes)

# CÉLULA 15

# Código para Curva de Aprendizado
from sklearn.model_selection import learning_curve
import matplotlib.pyplot as plt
import numpy as np

# Gerar curva de aprendizado
train_sizes, train_scores, val_scores = learning_curve(
    model_final, X_train_treated, y_train, 
    cv=5, n_jobs=-1, 
    train_sizes=np.linspace(0.1, 1.0, 10),
    scoring='accuracy'
)

# Calcular médias e desvios
train_mean = np.mean(train_scores, axis=1)
train_std = np.std(train_scores, axis=1)
val_mean = np.mean(val_scores, axis=1)
val_std = np.std(val_scores, axis=1)

# Plotar
plt.figure(figsize=(12, 5))

plt.subplot(1, 2, 1)
plt.plot(train_sizes, train_mean, 'o-', color='blue', label='Score de Treino')
plt.fill_between(train_sizes, train_mean - train_std, train_mean + train_std, alpha=0.1, color='blue')
plt.plot(train_sizes, val_mean, 'o-', color='red', label='Score de Validação')
plt.fill_between(train_sizes, val_mean - val_std, val_mean + val_std, alpha=0.1, color='red')
plt.title('Curva de Aprendizado - Gradient Boosting Classifier')
plt.xlabel('Número de Amostras de Treino')
plt.ylabel('Acurácia')
plt.legend()
plt.grid(True, alpha=0.3)
# Código para Matriz de Confusão
from sklearn.metrics import confusion_matrix
import seaborn as sns

# Calcular matriz de confusão
cm = confusion_matrix(y_test, y_pred)

# Plotar
fig, (ax2) = plt.subplots(1, figsize=(7, 5))

# Porcentagens
cm_percent = cm.astype('float') / cm.sum(axis=1)[:, np.newaxis]
sns.heatmap(cm_percent, annot=True, fmt='.2%', cmap='Blues', ax=ax2,
            xticklabels=['Não Comprou', 'Comprou'],
            yticklabels=['Não Comprou', 'Comprou'])
ax2.set_title('Porcentagens (% de Acerto por Categoria)')
ax2.set_xlabel('Predito')
ax2.set_ylabel('Real')

plt.tight_layout()
plt.show()

print(f"Acurácia geral: {accuracy_score(y_test, y_pred):.1%}")
print(f"Taxa de acerto 'Não Comprou': {cm[0,0]/(cm[0,0]+cm[0,1]):.1%}")
print(f"Taxa de acerto 'Comprou': {cm[1,1]/(cm[1,0]+cm[1,1]):.1%}")

# CÉLULA 16

# ANÁLISE DO SILHOUETTE SCORE PARA VALIDAÇÃO DOS CLUSTERS

import matplotlib.pyplot as plt
import numpy as np
from sklearn.metrics import silhouette_score, silhouette_samples
from sklearn.cluster import KMeans
from sklearn.preprocessing import StandardScaler
import seaborn as sns

print("=== ANÁLISE DO SILHOUETTE SCORE ===\n")

# Verificar se temos os dados necessários
if 'df_clientes_nacional' not in globals():
    print("ERRO: Dataset df_clientes_nacional não encontrado.")
    print("Execute primeiro as células de preparação dos dados.")
else:
    # Preparar dados para análise
    features_para_cluster_platinum = [
        'Frequencia', 'Monetaridade_Total', 'Ticket_Medio', 'Quantidade_Total', 
        'Pct_Compras_Com_Desconto', 'Pct_Compras_Canal_Otica', 'Pct_Compras_Loja_Rua',
        'Pct_Compras_Quiosque', 'Pct_Compras_Loja_Shopping', 'N_Lojas_Unicas', 
        'Recencia', 'Tempo_Como_Cliente', 'Idade', 'Itens_Por_Transacao', 
        'Preco_Medio_Item', 'Cadencia_Compra'
    ]
    
    # Preparar dados (usar amostra se dataset for muito grande para performance)
    sample_size = min(5000, len(df_clientes_nacional))  # Limitar a 5000 para performance
    df_sample = df_clientes_nacional.sample(n=sample_size, random_state=42)
    
    data_cluster = df_sample[features_para_cluster_platinum].dropna()
    print(f"Dados preparados: {len(data_cluster)} clientes para análise")
    
    # Padronizar os dados
    scaler = StandardScaler()
    data_scaled = scaler.fit_transform(data_cluster)
    
    # 1. ANÁLISE DO SILHOUETTE SCORE PARA DIFERENTES NÚMEROS DE CLUSTERS
    print("\n1. CALCULANDO SILHOUETTE SCORES PARA DIFERENTES NÚMEROS DE CLUSTERS...\n")
    
    range_clusters = range(2, 11)  # De 2 a 10 clusters
    silhouette_scores = []
    
    for n_clusters in range_clusters:
        # Aplicar KMeans
        kmeans = KMeans(n_clusters=n_clusters, init='k-means++', random_state=42, n_init=10)
        cluster_labels = kmeans.fit_predict(data_scaled)
        
        # Calcular Silhouette Score
        silhouette_avg = silhouette_score(data_scaled, cluster_labels)
        silhouette_scores.append(silhouette_avg)
        
        print(f"Para {n_clusters} clusters: Silhouette Score = {silhouette_avg:.4f}")
    
    # Encontrar o melhor número de clusters
    best_n_clusters = range_clusters[np.argmax(silhouette_scores)]
    best_score = max(silhouette_scores)
    print(f"\nMELHOR RESULTADO: {best_n_clusters} clusters com Silhouette Score = {best_score:.4f}")
    
    # 2. VISUALIZAÇÃO DOS RESULTADOS
    print("\n2. GERANDO VISUALIZAÇÕES...\n")
    
    # Gráfico do Silhouette Score por número de clusters
    plt.figure(figsize=(15, 5))
    
    # Subplot 1: Silhouette Score vs Número de Clusters
    plt.subplot(1, 3, 1)
    plt.plot(range_clusters, silhouette_scores, 'bo-', linewidth=2, markersize=8)
    plt.axhline(y=best_score, color='r', linestyle='--', alpha=0.7, label=f'Melhor Score: {best_score:.4f}')
    plt.axvline(x=best_n_clusters, color='r', linestyle='--', alpha=0.7, label=f'Melhor K: {best_n_clusters}')
    plt.title('Silhouette Score vs Número de Clusters')
    plt.xlabel('Número de Clusters (k)')
    plt.ylabel('Silhouette Score')
    plt.grid(True, alpha=0.3)
    plt.legend()
    plt.xticks(range_clusters)
    
    
    print("Visualizações geradas com sucesso!")

# CÉLULA 17

# ANÁLISE DETALHADA DO SILHOUETTE SCORE POR CLUSTER

if 'best_n_clusters' in globals() and 'data_scaled' in globals():
    print(f"=== ANÁLISE DETALHADA PARA {best_n_clusters} CLUSTERS ===\n")
    
    # Aplicar KMeans com o melhor número de clusters
    kmeans_best = KMeans(n_clusters=best_n_clusters, init='k-means++', random_state=42, n_init=10)
    cluster_labels_best = kmeans_best.fit_predict(data_scaled)
    
    # Calcular Silhouette Score para cada amostra
    sample_silhouette_values = silhouette_samples(data_scaled, cluster_labels_best)
    
    # 3. ANÁLISE POR CLUSTER INDIVIDUAL
    print("3. QUALIDADE DE CADA CLUSTER INDIVIDUAL:\n")
    
    cluster_analysis = []
    for i in range(best_n_clusters):
        cluster_silhouette_values = sample_silhouette_values[cluster_labels_best == i]
        cluster_size = len(cluster_silhouette_values)
        cluster_avg_score = cluster_silhouette_values.mean()
        cluster_std_score = cluster_silhouette_values.std()
        cluster_min_score = cluster_silhouette_values.min()
        cluster_max_score = cluster_silhouette_values.max()
        
        cluster_analysis.append({
            'cluster': i,
            'size': cluster_size,
            'avg_score': cluster_avg_score,
            'std_score': cluster_std_score,
            'min_score': cluster_min_score,
            'max_score': cluster_max_score
        })
        
        print(f"Cluster {i}:")
        print(f"  • Tamanho: {cluster_size} clientes ({cluster_size/len(data_scaled)*100:.1f}%)")
        print(f"  • Score Médio: {cluster_avg_score:.4f}")
        print(f"  • Desvio Padrão: {cluster_std_score:.4f}")
        print(f"  • Score Mínimo: {cluster_min_score:.4f}")
        print(f"  • Score Máximo: {cluster_max_score:.4f}")
        
        # Classificar qualidade do cluster
        if cluster_avg_score > 0.5:
            quality = "EXCELENTE"
        elif cluster_avg_score > 0.3:
            quality = "BOA"
        elif cluster_avg_score > 0.1:
            quality = "RAZOÁVEL"
        else:
            quality = "PROBLEMÁTICA"
        
        print(f"  • Qualidade: {quality}")
        print()
    
    # 4. VISUALIZAÇÃO DO SILHOUETTE PLOT
    print("4. GERANDO SILHOUETTE PLOT DETALHADO...\n")
    
    fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(18, 8))
    
    # Silhouette Plot
    y_lower = 10
    colors = plt.cm.Set3(np.linspace(0, 1, best_n_clusters))
    
    for i in range(best_n_clusters):
        cluster_silhouette_values = sample_silhouette_values[cluster_labels_best == i]
        cluster_silhouette_values.sort()
        
        size_cluster_i = cluster_silhouette_values.shape[0]
        y_upper = y_lower + size_cluster_i
        
        color = colors[i]
        ax1.fill_betweenx(np.arange(y_lower, y_upper),
                         0, cluster_silhouette_values,
                         facecolor=color, edgecolor=color, alpha=0.7)
        
        # Marcar o centro do cluster
        ax1.text(-0.05, y_lower + 0.5 * size_cluster_i, str(i))
        
        y_lower = y_upper + 10
    
    ax1.set_xlabel('Valores do Silhouette Score')
    ax1.set_ylabel('Índice das Amostras')
    ax1.set_title(f'Silhouette Plot para {best_n_clusters} Clusters')
    
    # Linha vertical para o score médio
    ax1.axvline(x=silhouette_score(data_scaled, cluster_labels_best), 
                color="red", linestyle="--", linewidth=2,
                label=f'Score Médio: {silhouette_score(data_scaled, cluster_labels_best):.4f}')
    ax1.legend()
    
    # Gráfico de barras com scores por cluster
    cluster_ids = [ca['cluster'] for ca in cluster_analysis]
    cluster_scores = [ca['avg_score'] for ca in cluster_analysis]
    cluster_sizes = [ca['size'] for ca in cluster_analysis]
    
    bars = ax2.bar(cluster_ids, cluster_scores, color=colors, alpha=0.7)
    ax2.set_xlabel('Número do Cluster')
    ax2.set_ylabel('Silhouette Score Médio')
    ax2.set_title('Score Médio por Cluster')
    ax2.grid(True, alpha=0.3)
    
    # Adicionar rótulos com tamanho do cluster
    for i, (bar, size) in enumerate(zip(bars, cluster_sizes)):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 0.01,
                f'n={size}', ha='center', va='bottom', fontsize=9)
    
    # Linha horizontal para o score médio geral
    ax2.axhline(y=silhouette_score(data_scaled, cluster_labels_best), 
                color="red", linestyle="--", alpha=0.7,
                label=f'Score Médio Geral: {silhouette_score(data_scaled, cluster_labels_best):.4f}')
    ax2.legend()
    
    plt.tight_layout()
    plt.show()
    
    # 5. IDENTIFICAÇÃO DE CLIENTES PROBLEMÁTICOS
    print("5. IDENTIFICAÇÃO DE CLIENTES COM BAIXO SILHOUETTE SCORE:\n")
    
    # Encontrar clientes com scores muito baixos (possível má classificação)
    threshold_low = 0.1  # Threshold para considerar score baixo
    problematic_indices = np.where(sample_silhouette_values < threshold_low)[0]
    
    print(f"Clientes com Silhouette Score < {threshold_low}: {len(problematic_indices)} ({len(problematic_indices)/len(sample_silhouette_values)*100:.1f}%)")
    
    if len(problematic_indices) > 0:
        print("\nDistribuição dos clientes problemáticos por cluster:")
        for i in range(best_n_clusters):
            cluster_problematic = np.sum((cluster_labels_best[problematic_indices] == i))
            cluster_total = np.sum(cluster_labels_best == i)
            print(f"  Cluster {i}: {cluster_problematic}/{cluster_total} ({cluster_problematic/cluster_total*100:.1f}%)")
    
    # 6. RESUMO FINAL
    print(f"\n=== RESUMO DA ANÁLISE SILHOUETTE ===")
    print(f"• Melhor número de clusters: {best_n_clusters}")
    print(f"• Silhouette Score médio: {silhouette_score(data_scaled, cluster_labels_best):.4f}")
    print(f"• Cluster com melhor qualidade: {max(cluster_analysis, key=lambda x: x['avg_score'])['cluster']}")
    print(f"• Cluster com pior qualidade: {min(cluster_analysis, key=lambda x: x['avg_score'])['cluster']}")
    print(f"• Clientes com possível má classificação: {len(problematic_indices)} ({len(problematic_indices)/len(sample_silhouette_values)*100:.1f}%)")
    
    # Interpretação dos resultados
    overall_score = silhouette_score(data_scaled, cluster_labels_best)
    if overall_score > 0.7:
        interpretation = "EXCELENTE - Clusters muito bem definidos"
    elif overall_score > 0.5:
        interpretation = "BOA - Clusters razoavelmente bem definidos"
    elif overall_score > 0.3:
        interpretation = "MODERADA - Clusters com sobreposição moderada"
    elif overall_score > 0.1:
        interpretation = "FRACA - Clusters com muita sobreposição"
    else:
        interpretation = "MUITO FRACA - Clustering pode não ser apropriado"
    
    print(f"• Interpretação geral: {interpretation}")
    
else:
    print("ERRO: Execute primeiro a análise anterior para obter os dados necessários.")

# CÉLULA 18

# INSIGHTS ADICIONAIS E RESUMO EXECUTIVO DA ANÁLISE SILHOUETTE

import pandas as pd

if 'best_n_clusters' in globals() and 'cluster_labels_best' in globals():
    print("=== RESUMO EXECUTIVO - ANÁLISE SILHOUETTE ===\n")
    
    # Criar DataFrame com resumo dos resultados
    results_summary = pd.DataFrame({
        'Métrica': [
            'Número de Clusters Analisados',
            'Melhor Número de Clusters',
            'Melhor Silhouette Score',
            'Clientes Analisados',
            'Clusters de Qualidade Excelente',
            'Clusters de Qualidade Boa',
            'Clusters de Qualidade Razoável',
            'Clusters Problemáticos',
            'Clientes Mal Classificados (%)',
            'Maior Cluster (% do total)',
            'Menor Cluster (% do total)',
            'Interpretação Geral'
        ],
        'Valor': [
            '2 a 10',
            f'{best_n_clusters}',
            f'{silhouette_score(data_scaled, cluster_labels_best):.4f}',
            f'{len(data_scaled):,}',
            '1 (Cluster 4)',
            '1 (Cluster 5)', 
            '2 (Clusters 0,1)',
            '2 (Clusters 2,3)',
            '7.3%',
            '55.1% (Cluster 0)',
            '0.0% (Cluster 3)',
            'FRACA - Clusters com sobreposição'
        ]
    })
    
    print("📊 RESUMO DOS RESULTADOS:")
    print("=" * 50)
    for idx, row in results_summary.iterrows():
        print(f"{row['Métrica']:<35}: {row['Valor']}")
    
    # Análise dos clusters por qualidade
    print(f"\n🎯 ANÁLISE POR QUALIDADE DE CLUSTER:")
    print("=" * 50)
    
    cluster_quality = {
        'Excelente (Score > 0.5)': [4],
        'Boa (Score 0.3-0.5)': [5],
        'Razoável (Score 0.1-0.3)': [0, 1],
        'Problemática (Score < 0.1)': [2, 3]
    }
    
    for quality, clusters in cluster_quality.items():
        total_clients = sum([np.sum(cluster_labels_best == c) for c in clusters])
        pct_clients = (total_clients / len(cluster_labels_best)) * 100
        print(f"{quality:<25}: {len(clusters)} clusters, {total_clients:,} clientes ({pct_clients:.1f}%)")
    
    # Recomendações específicas
    print(f"\n💡 RECOMENDAÇÕES ESPECÍFICAS:")
    print("=" * 50)
    
    recommendations = [
        "✅ PRIORIZAR Cluster 4 (21 clientes): Qualidade excelente, estratégias premium",
        "✅ OTIMIZAR Cluster 5 (658 clientes): Boa qualidade, campanhas direcionadas", 
        "⚠️  REVISAR Clusters 0 e 1 (4.059 clientes): Qualidade razoável, melhorar segmentação",
        "❌ REPROJETAR Clusters 2 e 3 (262 clientes): Qualidade problemática, reclassificar",
        "🔍 INVESTIGAR 366 clientes mal classificados: Analisar perfis individuais",
        "📈 CONSIDERAR reduzir para 4-5 clusters para melhor coesão",
        "🔄 REAVALIAR features utilizadas na clusterização"
    ]
    
    for rec in recommendations:
        print(f"  {rec}")
    
    # Análise de distribuição de tamanhos
    print(f"\n📏 DISTRIBUIÇÃO DE TAMANHOS DOS CLUSTERS:")
    print("=" * 50)
    
    cluster_sizes = [np.sum(cluster_labels_best == i) for i in range(best_n_clusters)]
    cluster_sizes_pct = [(size/len(cluster_labels_best))*100 for size in cluster_sizes]
    
    size_df = pd.DataFrame({
        'Cluster': range(best_n_clusters),
        'Tamanho': cluster_sizes,
        'Percentual': [f"{pct:.1f}%" for pct in cluster_sizes_pct],
        'Categoria': ['Dominante' if pct > 50 else 'Grande' if pct > 20 else 'Médio' if pct > 5 else 'Pequeno' if pct > 1 else 'Muito Pequeno' for pct in cluster_sizes_pct]
    })
    
    print(size_df.to_string(index=False))
    
    # Análise de desequilíbrio
    print(f"\n⚖️  ANÁLISE DE DESEQUILÍBRIO:")
    print("=" * 50)
    
    largest_cluster_pct = max(cluster_sizes_pct)
    smallest_cluster_pct = min([pct for pct in cluster_sizes_pct if pct > 0])
    imbalance_ratio = largest_cluster_pct / smallest_cluster_pct
    
    print(f"Maior cluster: {largest_cluster_pct:.1f}% dos dados")
    print(f"Menor cluster (válido): {smallest_cluster_pct:.1f}% dos dados") 
    print(f"Razão de desequilíbrio: {imbalance_ratio:.1f}:1")
    
    if imbalance_ratio > 100:
        balance_status = "MUITO DESEQUILIBRADO"
    elif imbalance_ratio > 50:
        balance_status = "DESEQUILIBRADO"
    elif imbalance_ratio > 10:
        balance_status = "MODERADAMENTE DESEQUILIBRADO"
    else:
        balance_status = "EQUILIBRADO"
    
    print(f"Status: {balance_status}")
    
    # Salvamento de resultados (opcional)
    print(f"\n💾 DADOS DISPONÍVEIS PARA EXPORTAÇÃO:")
    print("=" * 50)
    print("• best_n_clusters: Número ideal de clusters")
    print("• silhouette_scores: Scores para todos os números testados")
    print("• cluster_labels_best: Rótulos dos clusters para cada cliente")
    print("• sample_silhouette_values: Scores individuais de cada cliente")
    print("• results_summary: DataFrame com resumo dos resultados")
    
else:
    print("ERRO: Execute primeiro as células de análise Silhouette para obter os resultados.")