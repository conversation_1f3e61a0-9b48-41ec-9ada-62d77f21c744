{"cells": [{"cell_type": "markdown", "id": "main-title", "metadata": {}, "source": ["# Modelo Preditivo Híbrido - Chilli Beans\n", "\n", "## Análise Preditiva para Expansão de Óticas Especializadas em Óculos de Grau\n", "\n", "**Objetivo de Negócio:** Responder à pergunta estratégica \"Qual é a melhor cidade para abrir uma nova loja especializada em óculos de grau?\" através de um modelo híbrido que combina aprendizado não supervisionado (segmentação de mercado) com aprendizado supervisionado (predição de performance).\n", "\n", "**Abordagem Metodológica:**\n", "- **Fase 1:** Aprendizado Não Supervisionado - Clustering para segmentação de mercado\n", "- **Fase 2:** Aprendizado Supervisionado - Predição de performance de lojas\n", "\n", "**Contexto Empresarial:** A Chilli Beans busca expandir sua rede de óticas especializadas em óculos de grau, identificando localizações com maior potencial de sucesso baseado em padrões de comportamento do consumidor e características demográficas regionais."]}, {"cell_type": "markdown", "id": "imports-section", "metadata": {}, "source": ["## 1. Configuração do Ambiente e Importações\n", "\n", "### Conceitos de Machine Learning Aplicados:\n", "\n", "**Scikit-learn Pipeline:** Utilizaremos o padrão Pipeline do scikit-learn para criar fluxos de pré-processamento reproduzíveis e eficientes. O Pipeline garante que as transformações sejam aplicadas de forma consistente tanto nos dados de treino quanto nos de teste, evitando vazamento de dados (data leakage).\n", "\n", "**Modularização:** Importaremos funções dos módulos `data_filtering` e `data_preprocessing` para garantir consistência no tratamento dos dados e reutilização de código validado."]}, {"cell_type": "code", "execution_count": 609, "id": "imports", "metadata": {}, "outputs": [], "source": ["# Importações fundamentais para análise de dados\n", "import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "import seaborn as sns\n", "import warnings\n", "from typing import Dict, List, Tuple, Optional\n", "\n", "# Configuração de visualizações\n", "plt.style.use('seaborn-v0_8')\n", "sns.set_palette(\"husl\")\n", "warnings.filterwarnings('ignore')\n", "\n", "# Importação dos módulos de pré-processamento desenvolvidos\n", "# Estes módulos encapsulam as regras de negócio e transformações validadas\n", "import sys\n", "sys.path.append('..')\n", "from data_filtering import apply_business_filters\n", "from data_preprocessing import preprocess_data"]}, {"cell_type": "code", "execution_count": 610, "id": "sklearn-imports", "metadata": {}, "outputs": [], "source": ["# Importações do Scikit-learn para Machine Learning\n", "# <PERSON><PERSON><PERSON> as mel<PERSON>s práticas de Pipeline e modularização\n", "\n", "# === APRENDIZADO NÃO SUPERVISIONADO (Clustering) ===\n", "from sklearn.cluster import KMeans, DBSCAN, AgglomerativeClustering\n", "from sklearn.mixture import GaussianMixture\n", "\n", "# === APRENDIZADO SUPERVISIONADO (Predição) ===\n", "from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor\n", "from sklearn.linear_model import LinearRegression, Ridge\n", "from sklearn.tree import DecisionTreeRegressor\n", "\n", "# === PRÉ-PROCESSAMENTO E PIPELINE ===\n", "from sklearn.pipeline import Pipeline\n", "from sklearn.preprocessing import StandardScaler, MinMaxScaler, LabelEncoder\n", "from sklearn.compose import ColumnTransformer\n", "from sklearn.impute import SimpleImputer\n", "\n", "# === AVALIAÇÃO E VALIDAÇÃO ===\n", "from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV\n", "from sklearn.metrics import (\n", "    silhouette_score, calinski_harabasz_score, davies_bouldin_score,  # Clustering\n", "    mean_squared_error, mean_absolute_error, r2_score  # Regressão\n", ")\n", "\n", "# === REDUÇÃO DE DIMENSIONALIDADE ===\n", "from sklearn.decomposition import PCA\n", "from sklearn.manifold import TSNE"]}, {"cell_type": "markdown", "id": "data-loading-section", "metadata": {}, "source": ["## 2. Carregamento e Preparação dos Dados\n", "\n", "### Conceitos de Pré-processamento Aplicados:\n", "\n", "**Filtragem de Dados:** Aplicamos regras de negócio específicas da Chilli Beans para garantir qualidade dos dados, incluindo remoção de devoluções, validação de preços e idades, e eliminação de duplicatas.\n", "\n", "**Pipeline de Transformação:** Utilizamos o pipeline de pré-processamento desenvolvido que aplica:\n", "- Tratamento de valores ausentes com SimpleImputer\n", "- Detecção e tratamento de outliers usando método IQR\n", "- Normalização Min-Max e padronização Z-Score\n", "- Codificação de variáveis categóricas com OneHotEncoder e LabelEncoder\n", "\n", "**Foco em Óculos de Grau:** Filtraremos especificamente dados relacionados a óculos de grau para responder à questão de negócio sobre expansão de óticas especializadas."]}, {"cell_type": "code", "execution_count": 611, "id": "data-loading", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📥 Carregando dados da Chilli Beans...\n", "\n", "Dataset carregado pós filtragem por Ótica: 3,392 registros, 66 colunas\n"]}], "source": ["# Carregamento dos dados com aplicação das regras de negócio\n", "# O módulo data_filtering aplica filtros validados para garantir qualidade dos dados\n", "\n", "print(\"📥 Carregando dados da Chilli Beans...\")\n", "df_raw = apply_business_filters('../assets/dados.csv', verbose=False)\n", "df_raw = df_raw[df_raw['Dim_Lojas.CANAL_VENDA'] == 'OTICA']\n", "\n", "print(f\"\\nDataset carregado pós filtragem por Ótica: {df_raw.shape[0]:,} registros, {df_raw.shape[1]} colunas\")"]}, {"cell_type": "code", "execution_count": 612, "id": "e3a9244a", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Padronização de cidades concluída.\n", "• Cidades únicas antes: 124\n", "• Cidades únicas depois: 117\n", "• Variações unificadas (fuzzy): 7\n", "\n", "Exemplos de correções:\n", "  - SAO PAULO -> SÃO PAULO\n", "  - BRASÍLIA -> BRASILIA\n", "  - SÃO JOSÉ DOS CAMPOS -> SAO JOSE DOS CAMPOS\n", "  - MACEIO -> MACEIÓ\n", "  - MARINGÁ -> MARINGA\n", "  - FLORIANOPOLIS -> FLORIANÓPOLIS\n", "  - CUIABÁ -> CUIABA\n"]}], "source": ["from difflib import SequenceMatcher\n", "import unicodedata\n", "\n", "# Corrigir nomes de cidades semelhantes via fuzzy matching e padronizar para CAPS\n", "\n", "def strip_accents(text: str) -> str:\n", "    if not isinstance(text, str):\n", "        text = str(text)\n", "    return ''.join(\n", "        ch for ch in unicodedata.normalize('NFKD', text)\n", "        if not unicodedata.combining(ch)\n", "    )\n", "\n", "# Coluna alvo\n", "col_city = 'Dim_Lojas.Cidade_Emp'\n", "assert col_city in df_raw.columns, f\"Coluna '{col_city}' não encontrada em df_raw\"\n", "\n", "# Valores originais\n", "cities_series = df_raw[col_city].astype(str).str.strip()\n", "cities_upper = cities_series.str.upper()\n", "\n", "# Frequência de cada cidade (após upper)\n", "freq = cities_upper.value_counts()\n", "unique_cities = list(freq.index)\n", "\n", "# Agrupamento fuzzy em cima das cidades sem acentos\n", "threshold = 0.96  # limiar alto para evitar junções incorretas\n", "groups = []       # lista de grupos, cada grupo é um set de nomes (em CAPS)\n", "group_keys = []   # chave \"sem acento\" representativa de cada grupo\n", "\n", "for name in unique_cities:\n", "    key = strip_accents(name)\n", "    placed = False\n", "    # Tenta encontrar um grupo existente com chave similar\n", "    for i, gkey in enumerate(group_keys):\n", "        sim = SequenceMatcher(None, key, gkey).ratio()\n", "        if sim >= threshold:\n", "            groups[i].add(name)\n", "            placed = True\n", "            break\n", "    if not placed:\n", "        groups.append({name})\n", "        group_keys.append(key)\n", "\n", "# Escolher canônico por grupo: mais frequente; em caso de empate, preferir com acento\n", "canonical_per_group = []\n", "for members in groups:\n", "    members = list(members)\n", "    # Ordena por frequência (desc), depois preferir com acento\n", "    members_sorted = sorted(\n", "        members,\n", "        key=lambda n: (freq.get(n, 0), any(ord(c) > 127 for c in n)),\n", "        reverse=True\n", "    )\n", "    canonical_per_group.append(members_sorted[0])\n", "\n", "# Construir dicionário de mapeamento cidade -> canônico (todos em CAPSLOCK)\n", "mapping = {}\n", "for members, canonical in zip(groups, canonical_per_group):\n", "    for m in members:\n", "        mapping[m] = canonical\n", "\n", "# Aplicar mapeamento\n", "before_unique = cities_upper.nunique()\n", "df_raw[col_city] = cities_upper.map(mapping).fillna(cities_upper)\n", "after_unique = df_raw[col_city].nunique()\n", "\n", "# Relatório breve\n", "corrected_count = sum(1 for k, v in mapping.items() if k != v)\n", "print(f\"Padronização de cidades concluída.\")\n", "print(f\"• Cidades únicas antes: {before_unique}\")\n", "print(f\"• Cidades únicas depois: {after_unique}\")\n", "print(f\"• Variações unificadas (fuzzy): {corrected_count}\")\n", "\n", "# Exibir algumas correções aplicadas\n", "sample_changes = [(k, v) for k, v in mapping.items() if k != v][:10]\n", "if sample_changes:\n", "    print(\"\\nExemplos de correções:\")\n", "    for old, new in sample_changes:\n", "        print(f\"  - {old} -> {new}\")"]}, {"cell_type": "code", "execution_count": 613, "id": "prescription-glasses-filter", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["👓 Filtrando dados específicos de óculos de grau...\n", "\n", "Distribuição: <PERSON><PERSON><PERSON> de Grau vs Outros Produtos\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead tr th {\n", "        text-align: left;\n", "    }\n", "\n", "    .dataframe thead tr:last-of-type th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr>\n", "      <th></th>\n", "      <th colspan=\"2\" halign=\"left\">Quantidade</th>\n", "      <th colspan=\"2\" halign=\"left\">Valor_Total</th>\n", "      <th>ID_Cliente</th>\n", "    </tr>\n", "    <tr>\n", "      <th></th>\n", "      <th>count</th>\n", "      <th>sum</th>\n", "      <th>sum</th>\n", "      <th>mean</th>\n", "      <th>nunique</th>\n", "    </tr>\n", "    <tr>\n", "      <th>is_prescription_glasses</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>False</th>\n", "      <td>1859</td>\n", "      <td>1987</td>\n", "      <td>360969.75</td>\n", "      <td>194.17</td>\n", "      <td>1135</td>\n", "    </tr>\n", "    <tr>\n", "      <th>True</th>\n", "      <td>1533</td>\n", "      <td>2140</td>\n", "      <td>1026737.19</td>\n", "      <td>669.76</td>\n", "      <td>895</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        Quantidade       Valor_Total         ID_Cliente\n", "                             count   sum         sum    mean    nunique\n", "is_prescription_glasses                                                \n", "False                         1859  1987   360969.75  194.17       1135\n", "True                          1533  2140  1026737.19  669.76        895"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Lojas de ótica: 219\n", "Clientes únicos: 1724\n", "Cidades atendidas: 117\n"]}], "source": ["# Filtro específico para óculos de grau (lentes)\n", "# Este filtro é crucial para responder à pergunta de negócio sobre óticas especializadas\n", "\n", "print(\"👓 Filtrando dados específicos de óculos de grau...\")\n", "\n", "df_raw = df_raw[df_raw['Dim_Lojas.CANAL_VENDA'] == 'OTICA']\n", "\n", "# Identificar produtos relacionados a óculos de grau\n", "# Baseado na análise dos grupos de produtos disponíveis\n", "df_raw['produto_grupo_clean'] = df_raw['Dim_Produtos.Cod_Auxiliar'].astype(str).str.strip().str.upper()\n", "\n", "# Criar flag para óculos de grau (lentes)\n", "df_raw['is_prescription_glasses'] = df_raw['produto_grupo_clean'].str.contains('LV.', na=False) | df_raw['produto_grupo_clean'].str.contains('LE.', na=False)\n", "\n", "# Estatísticas sobre óculos de grau vs outros produtos\n", "prescription_stats = df_raw.groupby('is_prescription_glasses').agg({\n", "    'Quantidade': ['count', 'sum'],\n", "    'Valor_Total': ['sum', 'mean'],\n", "    'ID_Cliente': 'nunique'\n", "}).round(2)\n", "\n", "print(\"\\nDistribuição: <PERSON><PERSON><PERSON> de Grau vs Outros Produtos\")\n", "display(prescription_stats)\n", "\n", "print(f\"Lojas de ótica: {df_raw['ID_Loja'].nunique()}\")\n", "print(f\"Clientes únicos: {df_raw['ID_Cliente'].nunique()}\")\n", "print(f\"Cidades atendidas: {df_raw['Dim_Lojas.Cidade_Emp'].nunique()}\")"]}, {"cell_type": "markdown", "id": "phase1-title", "metadata": {}, "source": ["## 3. FASE 1: Aprendizado Não Supervisionado - Segmentação de Mercado\n", "\n", "### Conceitos de Clustering Aplicados:\n", "\n", "**K-Means Clustering:** Algoritmo de particionamento que agrupa dados em k clusters baseado na minimização da soma dos quadrados das distâncias aos centroides. Ideal para identificar segmentos de mercado com características similares.\n", "\n", "**Mé<PERSON><PERSON>tovelo (Elbow Method):** Técnica para determinar o número ótimo de clusters analisando a variação da inércia (WCSS - Within-Cluster Sum of Squares) conforme aumentamos k.\n", "\n", "**Silhouette Score:** Métrica que avalia a qualidade do clustering medindo quão similar um ponto é ao seu próprio cluster comparado aos outros clusters. Valores próximos a 1 indicam clustering bem definido.\n", "\n", "**Objetivo de Negócio:** Identificar segmentos de mercado baseados em características demográficas, geográficas e comportamentais para orientar a estratégia de expansão de óticas especializadas."]}, {"cell_type": "code", "execution_count": 614, "id": "feature-engineering", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Cidades removidas por baixa representatividade: 39\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Dim_Lojas.Cidade_Emp</th>\n", "      <th>Dim_Lojas.Estado_Emp</th>\n", "      <th>volume_total</th>\n", "      <th>volume_medio_transacao</th>\n", "      <th>num_transacoes</th>\n", "      <th>receita_total</th>\n", "      <th>ticket_medio</th>\n", "      <th>variabilidade_ticket</th>\n", "      <th>num_clientes_unicos</th>\n", "      <th>num_lojas</th>\n", "      <th>Dim_Cliente.Sexo_&lt;lambda&gt;</th>\n", "      <th>regiao</th>\n", "      <th>receita_per_store</th>\n", "      <th>clientes_per_store</th>\n", "      <th>volume_per_store</th>\n", "      <th>transacoes_per_store</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>ALTAMIRA</td>\n", "      <td>PA</td>\n", "      <td>2</td>\n", "      <td>1.00</td>\n", "      <td>2</td>\n", "      <td>419.96</td>\n", "      <td>209.98</td>\n", "      <td>268.70</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>F</td>\n", "      <td>NORTE</td>\n", "      <td>419.96</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "      <td>2.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>ARAÇATUBA</td>\n", "      <td>SP</td>\n", "      <td>8</td>\n", "      <td>1.14</td>\n", "      <td>7</td>\n", "      <td>1770.30</td>\n", "      <td>252.90</td>\n", "      <td>143.79</td>\n", "      <td>5</td>\n", "      <td>1</td>\n", "      <td>F</td>\n", "      <td>SUDESTE</td>\n", "      <td>1770.30</td>\n", "      <td>5.0</td>\n", "      <td>8.0</td>\n", "      <td>7.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>BARCARENA</td>\n", "      <td>PA</td>\n", "      <td>1</td>\n", "      <td>1.00</td>\n", "      <td>1</td>\n", "      <td>299.98</td>\n", "      <td>299.98</td>\n", "      <td>0.00</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>F</td>\n", "      <td>NORTE</td>\n", "      <td>299.98</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "      <td>1.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>BARUERI</td>\n", "      <td>SP</td>\n", "      <td>8</td>\n", "      <td>1.33</td>\n", "      <td>6</td>\n", "      <td>2069.94</td>\n", "      <td>344.99</td>\n", "      <td>217.76</td>\n", "      <td>4</td>\n", "      <td>1</td>\n", "      <td>M</td>\n", "      <td>SUDESTE</td>\n", "      <td>2069.94</td>\n", "      <td>4.0</td>\n", "      <td>8.0</td>\n", "      <td>6.0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>BOA VISTA</td>\n", "      <td>RR</td>\n", "      <td>10</td>\n", "      <td>1.25</td>\n", "      <td>8</td>\n", "      <td>2150.05</td>\n", "      <td>268.76</td>\n", "      <td>195.99</td>\n", "      <td>6</td>\n", "      <td>1</td>\n", "      <td>M</td>\n", "      <td>NORTE</td>\n", "      <td>2150.05</td>\n", "      <td>6.0</td>\n", "      <td>10.0</td>\n", "      <td>8.0</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Dim_Lojas.Cidade_Emp Dim_Lojas.Estado_Emp  volume_total  \\\n", "0              ALTAMIRA                   PA             2   \n", "6             ARAÇATUBA                   SP             8   \n", "9             BARCARENA                   PA             1   \n", "10              BARUERI                   SP             8   \n", "14            BOA VISTA                   RR            10   \n", "\n", "    volume_medio_transacao  num_transacoes  receita_total  ticket_medio  \\\n", "0                     1.00               2         419.96        209.98   \n", "6                     1.14               7        1770.30        252.90   \n", "9                     1.00               1         299.98        299.98   \n", "10                    1.33               6        2069.94        344.99   \n", "14                    1.25               8        2150.05        268.76   \n", "\n", "    variabilidade_ticket  num_clientes_unicos  num_lojas  \\\n", "0                 268.70                    2          1   \n", "6                 143.79                    5          1   \n", "9                   0.00                    1          1   \n", "10                217.76                    4          1   \n", "14                195.99                    6          1   \n", "\n", "   Dim_Cliente.Sexo_<lambda>   regiao  receita_per_store  clientes_per_store  \\\n", "0                          F    NORTE             419.96                 2.0   \n", "6                          F  SUDESTE            1770.30                 5.0   \n", "9                          F    NORTE             299.98                 1.0   \n", "10                         M  SUDESTE            2069.94                 4.0   \n", "14                         M    NORTE            2150.05                 6.0   \n", "\n", "    volume_per_store  transacoes_per_store  \n", "0                2.0                   2.0  \n", "6                8.0                   7.0  \n", "9                1.0                   1.0  \n", "10               8.0                   6.0  \n", "14              10.0                   8.0  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Features criadas para 78 cidades\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Dim_Lojas.Cidade_Emp</th>\n", "      <th>Dim_Lojas.Estado_Emp</th>\n", "      <th>volume_total</th>\n", "      <th>volume_medio_transacao</th>\n", "      <th>num_transacoes</th>\n", "      <th>receita_total</th>\n", "      <th>ticket_medio</th>\n", "      <th>variabilidade_ticket</th>\n", "      <th>num_clientes_unicos</th>\n", "      <th>num_lojas</th>\n", "      <th>Dim_Cliente.Sexo_&lt;lambda&gt;</th>\n", "      <th>regiao</th>\n", "      <th>receita_per_store</th>\n", "      <th>clientes_per_store</th>\n", "      <th>volume_per_store</th>\n", "      <th>transacoes_per_store</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>ANANINDEUA</td>\n", "      <td>PA</td>\n", "      <td>19</td>\n", "      <td>1.36</td>\n", "      <td>14</td>\n", "      <td>6447.74</td>\n", "      <td>460.55</td>\n", "      <td>359.20</td>\n", "      <td>7</td>\n", "      <td>1</td>\n", "      <td>F</td>\n", "      <td>NORTE</td>\n", "      <td>6447.74</td>\n", "      <td>7.000000</td>\n", "      <td>19.000000</td>\n", "      <td>14.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>ANÁPOLIS</td>\n", "      <td>GO</td>\n", "      <td>26</td>\n", "      <td>1.13</td>\n", "      <td>23</td>\n", "      <td>7325.14</td>\n", "      <td>318.48</td>\n", "      <td>270.98</td>\n", "      <td>11</td>\n", "      <td>1</td>\n", "      <td>M</td>\n", "      <td>CENTRO-OESTE</td>\n", "      <td>7325.14</td>\n", "      <td>11.000000</td>\n", "      <td>26.000000</td>\n", "      <td>23.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>APARECIDA</td>\n", "      <td>SP</td>\n", "      <td>36</td>\n", "      <td>1.03</td>\n", "      <td>35</td>\n", "      <td>6160.34</td>\n", "      <td>176.01</td>\n", "      <td>164.52</td>\n", "      <td>12</td>\n", "      <td>1</td>\n", "      <td>F</td>\n", "      <td>SUDESTE</td>\n", "      <td>6160.34</td>\n", "      <td>12.000000</td>\n", "      <td>36.000000</td>\n", "      <td>35.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>APARECIDA DE GOIÂNIA</td>\n", "      <td>GO</td>\n", "      <td>24</td>\n", "      <td>1.20</td>\n", "      <td>20</td>\n", "      <td>5572.37</td>\n", "      <td>278.62</td>\n", "      <td>333.93</td>\n", "      <td>8</td>\n", "      <td>1</td>\n", "      <td>F</td>\n", "      <td>CENTRO-OESTE</td>\n", "      <td>5572.37</td>\n", "      <td>8.000000</td>\n", "      <td>24.000000</td>\n", "      <td>20.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>ARACAJU</td>\n", "      <td>SE</td>\n", "      <td>41</td>\n", "      <td>1.17</td>\n", "      <td>35</td>\n", "      <td>12773.88</td>\n", "      <td>364.97</td>\n", "      <td>313.47</td>\n", "      <td>19</td>\n", "      <td>3</td>\n", "      <td>F</td>\n", "      <td>NORDESTE</td>\n", "      <td>4257.96</td>\n", "      <td>6.333333</td>\n", "      <td>13.666667</td>\n", "      <td>11.666667</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["   Dim_Lojas.Cidade_Emp Dim_Lojas.Estado_Emp  volume_total  \\\n", "1            ANANINDEUA                   PA            19   \n", "2              ANÁPOLIS                   GO            26   \n", "3             APARECIDA                   SP            36   \n", "4  APARECIDA DE GOIÂNIA                   GO            24   \n", "5               ARACAJU                   SE            41   \n", "\n", "   volume_medio_transacao  num_transacoes  receita_total  ticket_medio  \\\n", "1                    1.36              14        6447.74        460.55   \n", "2                    1.13              23        7325.14        318.48   \n", "3                    1.03              35        6160.34        176.01   \n", "4                    1.20              20        5572.37        278.62   \n", "5                    1.17              35       12773.88        364.97   \n", "\n", "   variabilidade_ticket  num_clientes_unicos  num_lojas  \\\n", "1                359.20                    7          1   \n", "2                270.98                   11          1   \n", "3                164.52                   12          1   \n", "4                333.93                    8          1   \n", "5                313.47                   19          3   \n", "\n", "  Dim_Cliente.Sexo_<lambda>        regiao  receita_per_store  \\\n", "1                         F         NORTE            6447.74   \n", "2                         M  CENTRO-OESTE            7325.14   \n", "3                         F       SUDESTE            6160.34   \n", "4                         F  CENTRO-OESTE            5572.37   \n", "5                         F      NORDESTE            4257.96   \n", "\n", "   clientes_per_store  volume_per_store  transacoes_per_store  \n", "1            7.000000         19.000000             14.000000  \n", "2           11.000000         26.000000             23.000000  \n", "3           12.000000         36.000000             35.000000  \n", "4            8.000000         24.000000             20.000000  \n", "5            6.333333         13.666667             11.666667  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Estatísticas descritivas das features numéricas:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>volume_total</th>\n", "      <th>volume_medio_transacao</th>\n", "      <th>num_transacoes</th>\n", "      <th>receita_total</th>\n", "      <th>ticket_medio</th>\n", "      <th>variabilidade_ticket</th>\n", "      <th>num_clientes_unicos</th>\n", "      <th>num_lojas</th>\n", "      <th>receita_per_store</th>\n", "      <th>clientes_per_store</th>\n", "      <th>volume_per_store</th>\n", "      <th>transacoes_per_store</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>78.000000</td>\n", "      <td>78.000000</td>\n", "      <td>78.000000</td>\n", "      <td>78.000000</td>\n", "      <td>78.000000</td>\n", "      <td>78.000000</td>\n", "      <td>78.000000</td>\n", "      <td>78.000000</td>\n", "      <td>78.000000</td>\n", "      <td>78.000000</td>\n", "      <td>78.000000</td>\n", "      <td>78.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>49.948718</td>\n", "      <td>1.194103</td>\n", "      <td>41.102564</td>\n", "      <td>17060.209872</td>\n", "      <td>376.696667</td>\n", "      <td>400.284487</td>\n", "      <td>20.576923</td>\n", "      <td>2.282051</td>\n", "      <td>7444.220878</td>\n", "      <td>9.878385</td>\n", "      <td>23.794546</td>\n", "      <td>20.027812</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>94.902260</td>\n", "      <td>0.106788</td>\n", "      <td>73.312636</td>\n", "      <td>38154.578513</td>\n", "      <td>112.596765</td>\n", "      <td>214.461224</td>\n", "      <td>36.294438</td>\n", "      <td>4.257606</td>\n", "      <td>4215.586049</td>\n", "      <td>5.168102</td>\n", "      <td>12.196817</td>\n", "      <td>10.590343</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>10.000000</td>\n", "      <td>1.000000</td>\n", "      <td>10.000000</td>\n", "      <td>1909.910000</td>\n", "      <td>159.160000</td>\n", "      <td>91.080000</td>\n", "      <td>4.000000</td>\n", "      <td>1.000000</td>\n", "      <td>1183.960000</td>\n", "      <td>3.000000</td>\n", "      <td>4.000000</td>\n", "      <td>4.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>20.250000</td>\n", "      <td>1.140000</td>\n", "      <td>16.250000</td>\n", "      <td>6116.315000</td>\n", "      <td>295.580000</td>\n", "      <td>234.577500</td>\n", "      <td>8.000000</td>\n", "      <td>1.000000</td>\n", "      <td>4250.370000</td>\n", "      <td>6.000000</td>\n", "      <td>13.750000</td>\n", "      <td>12.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>35.500000</td>\n", "      <td>1.210000</td>\n", "      <td>29.000000</td>\n", "      <td>10558.235000</td>\n", "      <td>374.240000</td>\n", "      <td>325.855000</td>\n", "      <td>14.500000</td>\n", "      <td>1.000000</td>\n", "      <td>6364.450000</td>\n", "      <td>8.750000</td>\n", "      <td>21.750000</td>\n", "      <td>17.416667</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>49.750000</td>\n", "      <td>1.250000</td>\n", "      <td>43.250000</td>\n", "      <td>16510.310000</td>\n", "      <td>458.725000</td>\n", "      <td>565.430000</td>\n", "      <td>21.000000</td>\n", "      <td>2.750000</td>\n", "      <td>9771.712500</td>\n", "      <td>12.475000</td>\n", "      <td>31.000000</td>\n", "      <td>25.375000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>842.000000</td>\n", "      <td>1.580000</td>\n", "      <td>648.000000</td>\n", "      <td>337904.510000</td>\n", "      <td>652.100000</td>\n", "      <td>1126.930000</td>\n", "      <td>321.000000</td>\n", "      <td>38.000000</td>\n", "      <td>24461.210000</td>\n", "      <td>32.000000</td>\n", "      <td>66.000000</td>\n", "      <td>60.000000</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       volume_total  volume_medio_transacao  num_transacoes  receita_total  \\\n", "count     78.000000               78.000000       78.000000      78.000000   \n", "mean      49.948718                1.194103       41.102564   17060.209872   \n", "std       94.902260                0.106788       73.312636   38154.578513   \n", "min       10.000000                1.000000       10.000000    1909.910000   \n", "25%       20.250000                1.140000       16.250000    6116.315000   \n", "50%       35.500000                1.210000       29.000000   10558.235000   \n", "75%       49.750000                1.250000       43.250000   16510.310000   \n", "max      842.000000                1.580000      648.000000  337904.510000   \n", "\n", "       ticket_medio  variabilidade_ticket  num_clientes_unicos  num_lojas  \\\n", "count     78.000000             78.000000            78.000000  78.000000   \n", "mean     376.696667            400.284487            20.576923   2.282051   \n", "std      112.596765            214.461224            36.294438   4.257606   \n", "min      159.160000             91.080000             4.000000   1.000000   \n", "25%      295.580000            234.577500             8.000000   1.000000   \n", "50%      374.240000            325.855000            14.500000   1.000000   \n", "75%      458.725000            565.430000            21.000000   2.750000   \n", "max      652.100000           1126.930000           321.000000  38.000000   \n", "\n", "       receita_per_store  clientes_per_store  volume_per_store  \\\n", "count          78.000000           78.000000         78.000000   \n", "mean         7444.220878            9.878385         23.794546   \n", "std          4215.586049            5.168102         12.196817   \n", "min          1183.960000            3.000000          4.000000   \n", "25%          4250.370000            6.000000         13.750000   \n", "50%          6364.450000            8.750000         21.750000   \n", "75%          9771.712500           12.475000         31.000000   \n", "max         24461.210000           32.000000         66.000000   \n", "\n", "       transacoes_per_store  \n", "count             78.000000  \n", "mean              20.027812  \n", "std               10.590343  \n", "min                4.000000  \n", "25%               12.000000  \n", "50%               17.416667  \n", "75%               25.375000  \n", "max               60.000000  "]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Engenharia de Features para Clustering\n", "# Criação de variáveis agregadas por cidade para análise de mercado\n", "\n", "# Agregar dados por cidade para análise de mercado\n", "# Cada linha representará uma cidade com suas características de mercado\n", "city_features = df_raw.groupby(['Dim_Lojas.Cidade_Emp', 'Dim_Lojas.Estado_Emp']).agg({\n", "    # Métricas de volume e performance\n", "    'Quantidade': ['sum', 'mean', 'count'],  # Volume total, médio e número de transações\n", "    'Valor_Total': ['sum', 'mean', 'std'],   # Receita total, ticket médio e variabilidade\n", "    \n", "    # Métricas de clientes\n", "    'ID_Cliente': 'nunique',                 # Número de clientes únicos\n", "    'ID_Loja': 'nunique',                    # Número de lojas na cidade\n", "    \n", "    # Características demográficas (moda para variáveis categóricas)\n", "    'Dim_Cliente.Sexo': lambda x: x.mode().iloc[0] if not x.mode().empty else 'M',\n", "    'Dim_Lojas.REGIAO_CHILLI': lambda x: x.mode().iloc[0] if not x.mode().empty else 'SUDESTE'\n", "}).round(2)\n", "\n", "# Flatten column names (remover multi-index)\n", "city_features.columns = ['_'.join(col).strip() if col[1] else col[0] for col in city_features.columns]\n", "city_features = city_features.reset_index()\n", "\n", "# Renomear colunas para melhor legibilidade\n", "column_mapping = {\n", "    'Quantidade_sum': 'volume_total',\n", "    'Quantidade_mean': 'volume_medio_transacao',\n", "    'Quantidade_count': 'num_transacoes',\n", "    'Valor_Total_sum': 'receita_total',\n", "    'Valor_Total_mean': 'ticket_medio',\n", "    'Valor_Total_std': 'variabilidade_ticket',    \n", "    'ID_Cliente_nunique': 'num_clientes_unicos',\n", "    'ID_Loja_nunique': 'num_lojas',\n", "    'Dim_Lojas.REGIAO_CHILLI_<lambda>': 'regiao'\n", "}\n", "\n", "city_features = city_features.rename(columns=column_mapping)\n", "\n", "# Tratar valores NaN resultantes de std em cidades com apenas 1 transação\n", "city_features['variabilidade_ticket'] = city_features['variabilidade_ticket'].fillna(0)\n", "\n", "# CORREÇÃO CRÍTICA: Métricas por loja para normalizar cidades de tamanhos diferentes\n", "city_features['receita_per_store'] = city_features['receita_total'] / city_features['num_lojas']\n", "city_features['clientes_per_store'] = city_features['num_clientes_unicos'] / city_features['num_lojas']\n", "city_features['volume_per_store'] = city_features['volume_total'] / city_features['num_lojas']\n", "city_features['transacoes_per_store'] = city_features['num_transacoes'] / city_features['num_lojas']\n", "\n", "min_lojas = 1\n", "min_clientes = 2\n", "min_transacoes = 10\n", "\n", "lower_potential_cities = city_features[\n", "    (city_features['num_lojas'] < min_lojas) |\n", "    (city_features['num_clientes_unicos'] < min_clientes) |\n", "    (city_features['num_transacoes'] < min_transacoes)\n", "]\n", "\n", "city_features = city_features[\n", "    (city_features['num_lojas'] >= min_lojas) &\n", "    (city_features['num_clientes_unicos'] >= min_clientes) &\n", "    (city_features['num_transacoes'] >= min_transacoes)\n", "].copy()\n", "\n", "print(f\"Cidades removidas por baixa representatividade: {len(lower_potential_cities)}\")\n", "display(lower_potential_cities.head())\n", "\n", "print(f\"\\nFeatures criadas para {len(city_features)} cidades\")\n", "display(city_features.head())\n", "\n", "print(\"\\nEstatísticas descritivas das features numéricas:\")\n", "numeric_cols = city_features.select_dtypes(include=[np.number]).columns\n", "display(city_features[numeric_cols].describe())"]}, {"cell_type": "code", "execution_count": 615, "id": "clustering-preparation", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Preparando dados para clustering com Pipeline do scikit-learn...\n"]}], "source": ["# Preparação dos dados para clustering usando Pipeline do scikit-learn\n", "# Aplicação das melhores práticas de pré-processamento\n", "\n", "print(\"Preparando dados para clustering com Pipeline do scikit-learn...\")\n", "\n", "# Selecionar features numéricas para clustering\n", "# Excluindo identificadores e variáveis categóricas que serão tratadas separadamente\n", "# Usaremos métricas \"per store\" para normalizar o efeito do número de lojas\n", "clustering_features = [\n", "    'receita_per_store', 'volume_per_store', 'ticket_medio'\n", "]\n", "\n", "# Criar Pipeline de pré-processamento para clustering\n", "# Pipeline garante aplicação consistente das transformações\n", "clustering_pipeline = Pipeline([\n", "    # Etapa 1: Imputação de valores ausentes com mediana (robusto a outliers)\n", "    ('imputer', SimpleImputer(strategy='median')),\n", "    \n", "    # Etapa 2: Padronização Z-Score para equalizar escalas das variáveis\n", "    # Essencial para algoritmos baseados em distância como K-Means\n", "    ('scaler', StandardScaler())\n", "])"]}, {"cell_type": "code", "execution_count": 616, "id": "8305ba24", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["📊 receita_total: 5 outliers detectados\n", "   • Outliers: ['BRASILIA', 'CAMPINAS', 'CAMPO GRANDE', 'CURITIBA', 'SÃO PAULO']\n", "📊 volume_total: 5 outliers detectados\n", "   • Outliers: ['CAMPINAS', 'CAMPO GRANDE', 'CURITIBA', 'RIO DE JANEIRO', 'SÃO PAULO']\n", "📊 volume_per_store: 2 outliers detectados\n", "   • Outliers: ['PARAUAPEBAS', 'PONTA GROSSA']\n", "\n", "Total de cidades outliers detectadas: 8\n", "Cidades outliers: ['CAMPO GRANDE', 'CAMPINAS', 'PONTA GROSSA', 'BRASILIA', 'RIO DE JANEIRO', 'CURITIBA', 'PARAUAPEBAS', 'SÃO PAULO']\n"]}], "source": ["# CORREÇÃO CRÍTICA: Tratamento Robusto de Outliers\n", "# Método estatístico para identificar e tratar outliers\n", "\n", "def detect_outliers_statistical(city_features, columns=['receita_total', 'volume_total', 'volume_per_store'], method='iqr'):\n", "    \"\"\"Detectar outliers usando método estatístico robusto\"\"\"\n", "    \n", "    outlier_cities = set()\n", "    \n", "    for column in columns:\n", "        if method == 'iqr':\n", "            Q1 = city_features[column].quantile(0.25)\n", "            Q3 = city_features[column].quantile(0.75)\n", "            IQR = Q3 - Q1\n", "            lower_bound = Q1 - 1.5 * IQR\n", "            upper_bound = Q3 + 1.5 * IQR\n", "            \n", "            column_outliers = city_features[\n", "                (city_features[column] < lower_bound) | \n", "                (city_features[column] > upper_bound)\n", "            ]['Dim_Lojas.Cidade_Emp'].tolist()\n", "            \n", "        elif method == 'zscore':\n", "            z_scores = np.abs((city_features[column] - city_features[column].mean()) / city_features[column].std())\n", "            column_outliers = city_features[z_scores > 3]['Dim_Lojas.Cidade_Emp'].tolist()\n", "        \n", "        outlier_cities.update(column_outliers)\n", "        \n", "        print(f\"📊 {column}: {len(column_outliers)} outliers detectados\")\n", "        if column_outliers:\n", "            print(f\"   • Outliers: {column_outliers}\")\n", "    \n", "    return list(outlier_cities)\n", "\n", "# Detectar outliers estatisticamente\n", "outlier_cities = detect_outliers_statistical(city_features)\n", "city_features = city_features[~city_features['Dim_Lojas.Cidade_Emp'].isin(outlier_cities)]\n", "\n", "print(f\"\\nTotal de cidades outliers detectadas: {len(outlier_cities)}\")\n", "print(f\"Cidades outliers: {outlier_cities}\")"]}, {"cell_type": "code", "execution_count": 617, "id": "7d33c7ae", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Dados preparados para clustering: (70, 3)\n", "Features utilizadas: ['receita_per_store', 'volume_per_store', 'ticket_medio']\n", "\n", "Verificação da padronização:\n", "Média das features padronizadas: [0. 0. 0.]\n", "<PERSON><PERSON> das features padronizadas: [1. 1. 1.]\n"]}], "source": ["# Aplicar pipeline de pré-processamento\n", "X_clustering = clustering_pipeline.fit_transform(city_features[clustering_features])\n", "\n", "print(f\"Dados preparados para clustering: {X_clustering.shape}\")\n", "print(f\"Features utilizadas: {clustering_features}\")\n", "\n", "# Verificar qualidade da padronização\n", "print(f\"\\nVerificação da padronização:\")\n", "print(f\"Média das features padronizadas: {np.mean(X_clustering, axis=0).round(3)}\")\n", "print(f\"<PERSON>vio padr<PERSON> das features padronizadas: {np.std(X_clustering, axis=0).round(3)}\")"]}, {"cell_type": "code", "execution_count": 618, "id": "optimal-clusters", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1500x500 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Número ótimo de clusters: 4\n", "<PERSON><PERSON> Score: 0.412\n", "\n", "Métricas por número de clusters:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>K</th>\n", "      <th>Inércia</th>\n", "      <th>Si<PERSON><PERSON>ette Score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>3</td>\n", "      <td>77.035888</td>\n", "      <td>0.408346</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>4</td>\n", "      <td>57.162731</td>\n", "      <td>0.363884</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>5</td>\n", "      <td>47.165857</td>\n", "      <td>0.330863</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>6</td>\n", "      <td>37.931804</td>\n", "      <td>0.369837</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>7</td>\n", "      <td>30.521864</td>\n", "      <td>0.381093</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>8</td>\n", "      <td>25.684296</td>\n", "      <td>0.382372</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>9</td>\n", "      <td>21.981651</td>\n", "      <td>0.394087</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>10</td>\n", "      <td>18.756531</td>\n", "      <td>0.411801</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["    <PERSON>    <PERSON><PERSON>\n", "0   3  77.035888          0.408346\n", "1   4  57.162731          0.363884\n", "2   5  47.165857          0.330863\n", "3   6  37.931804          0.369837\n", "4   7  30.521864          0.381093\n", "5   8  25.684296          0.382372\n", "6   9  21.981651          0.394087\n", "7  10  18.756531          0.411801"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Determinação do número ótimo de clusters\n", "# Utilizando método do cotovelo e silhouette score\n", "\n", "# Testar diferentes números de clusters\n", "k_range = range(3, 11) # Ignoramos o 1 porque não faz sentido\n", "inertias = []\n", "silhouette_scores = []\n", "\n", "for k in k_range:\n", "    # Aplicar K-Means com diferentes valores de k\n", "    kmeans = KMeans(n_clusters=k, random_state=42, n_init=10)\n", "    cluster_labels = kmeans.fit_predict(X_clustering)\n", "    \n", "    # Calcular métricas de avaliação\n", "    inertias.append(kmeans.inertia_)  # WCSS (Within-Cluster Sum of Squares)\n", "    silhouette_scores.append(silhouette_score(X_clustering, cluster_labels))\n", "    \n", "# Visualizar métricas para determinação do k ótimo\n", "fig, (ax1, ax2) = plt.subplots(1, 2, figsize=(15, 5))\n", "\n", "# Gráfico do método do cotovelo\n", "ax1.plot(k_range, inertias, 'bo-', linewidth=2, markersize=8)\n", "ax1.set_xlabel('Número de Clusters (k)')\n", "ax1.set_ylabel('Iné<PERSON> (WCSS)')\n", "ax1.set_title('Método do Cotovelo para Determinação do K Ótimo')\n", "ax1.grid(True, alpha=0.3)\n", "\n", "# Gráfico do silhouette score\n", "ax2.plot(k_range, silhouette_scores, 'ro-', linewidth=2, markersize=8)\n", "ax2.set_xlabel('Número de Clusters (k)')\n", "ax2.set_ylabel('Silhouette Score')\n", "ax2.set_title('<PERSON><PERSON><PERSON>ette Score por Número de Clusters')\n", "ax2.grid(True, alpha=0.3)\n", "\n", "plt.tight_layout()\n", "plt.show()\n", "\n", "# Encontrar k ótimo baseado no maior silhouette score\n", "optimal_k = 4\n", "best_silhouette = max(silhouette_scores)\n", "\n", "print(f\"\\nNúmero ótimo de clusters: {optimal_k}\")\n", "print(f\"<PERSON><PERSON> Score: {best_silhouette:.3f}\")\n", "\n", "# Mostrar tabela com todas as mé<PERSON>as\n", "metrics_df = pd.DataFrame({\n", "    'K': k_range,\n", "    'Inércia': inertias,\n", "    'Silhouette Score': silhouette_scores\n", "})\n", "print(\"\\nMétricas por número de clusters:\")\n", "display(metrics_df)"]}, {"cell_type": "code", "execution_count": 619, "id": "final-clustering", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Aplicando K-Means com 4 clusters...\n", "\n", "Métricas de qualidade do clustering final:\n", "   • Silhouette Score: 0.364 (quanto maior, melhor)\n", "   • Calinski-Harabasz Score: 58.822 (quanto maior, melhor)\n", "   • <PERSON>-<PERSON><PERSON><PERSON> Score: 0.918 (quanto menor, melhor)\n", "\n", "Características dos clusters:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>num_cidades</th>\n", "      <th>volume_total_mean</th>\n", "      <th>volume_total_std</th>\n", "      <th>receita_total_mean</th>\n", "      <th>receita_total_std</th>\n", "      <th>ticket_medio_mean</th>\n", "      <th>ticket_medio_std</th>\n", "      <th>num_clientes_unicos_mean</th>\n", "      <th>num_clientes_unicos_std</th>\n", "      <th>num_lojas_mean</th>\n", "      <th>num_lojas_std</th>\n", "      <th>regiao_predominante</th>\n", "    </tr>\n", "    <tr>\n", "      <th>cluster</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>25</td>\n", "      <td>20.56</td>\n", "      <td>12.27</td>\n", "      <td>5182.39</td>\n", "      <td>3497.88</td>\n", "      <td>281.27</td>\n", "      <td>57.87</td>\n", "      <td>9.64</td>\n", "      <td>6.21</td>\n", "      <td>1.52</td>\n", "      <td>0.96</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>14</td>\n", "      <td>48.64</td>\n", "      <td>16.75</td>\n", "      <td>18147.23</td>\n", "      <td>6150.88</td>\n", "      <td>469.35</td>\n", "      <td>73.71</td>\n", "      <td>20.14</td>\n", "      <td>7.36</td>\n", "      <td>1.43</td>\n", "      <td>0.51</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>22</td>\n", "      <td>35.27</td>\n", "      <td>14.65</td>\n", "      <td>12615.42</td>\n", "      <td>5128.67</td>\n", "      <td>448.80</td>\n", "      <td>72.85</td>\n", "      <td>15.14</td>\n", "      <td>7.33</td>\n", "      <td>2.05</td>\n", "      <td>0.79</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>9</td>\n", "      <td>42.89</td>\n", "      <td>17.43</td>\n", "      <td>10651.72</td>\n", "      <td>5902.98</td>\n", "      <td>282.04</td>\n", "      <td>72.32</td>\n", "      <td>15.44</td>\n", "      <td>6.42</td>\n", "      <td>1.22</td>\n", "      <td>0.67</td>\n", "      <td>NORTE</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["         num_cidades  volume_total_mean  volume_total_std  receita_total_mean  \\\n", "cluster                                                                         \n", "0                 25              20.56             12.27             5182.39   \n", "1                 14              48.64             16.75            18147.23   \n", "2                 22              35.27             14.65            12615.42   \n", "3                  9              42.89             17.43            10651.72   \n", "\n", "         receita_total_std  ticket_medio_mean  ticket_medio_std  \\\n", "cluster                                                           \n", "0                  3497.88             281.27             57.87   \n", "1                  6150.88             469.35             73.71   \n", "2                  5128.67             448.80             72.85   \n", "3                  5902.98             282.04             72.32   \n", "\n", "         num_clientes_unicos_mean  num_clientes_unicos_std  num_lojas_mean  \\\n", "cluster                                                                      \n", "0                            9.64                     6.21            1.52   \n", "1                           20.14                     7.36            1.43   \n", "2                           15.14                     7.33            2.05   \n", "3                           15.44                     6.42            1.22   \n", "\n", "         num_lojas_std regiao_predominante  \n", "cluster                                     \n", "0                 0.96             SUDESTE  \n", "1                 0.51             SUDESTE  \n", "2                 0.79             SUDESTE  \n", "3                 0.67               NORTE  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "Exemplos de cidades por cluster:\n", "\n", "Cluster 0 (Top 5 cidades por receita):\n", "   • MACEIÓ/AL - R$ 16,668.68\n", "   • ARACAJU/SE - R$ 12,773.88\n", "   • BELO HORIZONTE/MG - R$ 10,178.90\n", "   • FLORIANÓPOLIS/SC - R$ 7,667.26\n", "   • OSASCO/SP - R$ 6,970.43\n", "\n", "Cluster 1 (Top 5 cidades por receita):\n", "   • MANAUS/AM - R$ 29,089.16\n", "   • SANTO ANDRÉ/SP - R$ 26,254.85\n", "   • SANTOS/SP - R$ 24,601.52\n", "   • LONDRINA/PR - R$ 23,983.62\n", "   • SAO JOSE DOS CAMPOS/SP - R$ 22,694.09\n", "\n", "Cluster 2 (Top 5 cidades por receita):\n", "   • GOIANIA/GO - R$ 22,614.19\n", "   • RECIFE/PE - R$ 22,107.12\n", "   • PRAIA GRANDE/SP - R$ 19,110.25\n", "   • JUIZ DE FORA/MG - R$ 18,672.38\n", "   • LAGES/SC - R$ 16,878.90\n", "\n", "Cluster 3 (Top 5 cidades por receita):\n", "   • BELÉM/PA - R$ 25,811.23\n", "   • PORTO VELHO/RO - R$ 11,288.02\n", "   • SINOP/MT - R$ 9,782.81\n", "   • BALNEÁRIO CAMBORIÚ/SC - R$ 9,653.96\n", "   • CANOAS/RS - R$ 9,520.70\n"]}], "source": ["# Aplicação do clustering final com k ótimo\n", "# <PERSON><PERSON><PERSON><PERSON> de<PERSON>a dos segmentos de mercado identificados\n", "\n", "print(f\"Aplicando K-Means com {optimal_k} clusters...\")\n", "\n", "# Aplicar K-Means final com k ótimo\n", "final_kmeans = KMeans(n_clusters=optimal_k, random_state=42, n_init=10)\n", "cluster_labels = final_kmeans.fit_predict(X_clustering)\n", "\n", "# Adicionar labels dos clusters ao dataset original\n", "city_features['cluster'] = cluster_labels\n", "\n", "# Calcular métricas finais de qualidade do clustering\n", "final_silhouette = silhouette_score(X_clustering, cluster_labels)\n", "final_calinski = calinski_harabasz_score(X_clustering, cluster_labels)\n", "final_davies = davies_bouldin_score(X_clustering, cluster_labels)\n", "\n", "print(f\"\\nMétricas de qualidade do clustering final:\")\n", "print(f\"   • Silhouette Score: {final_silhouette:.3f} (quanto maior, melhor)\")\n", "print(f\"   • Calinski-Harabasz Score: {final_calinski:.3f} (quanto maior, melhor)\")\n", "print(f\"   • <PERSON><PERSON><PERSON><PERSON><PERSON> Score: {final_davies:.3f} (quanto menor, melhor)\")\n", "\n", "cluster_analysis = city_features.groupby('cluster').agg({\n", "    'Dim_Lojas.Cidade_Emp': 'count',  # Número de cidades no cluster\n", "    'volume_total': ['mean', 'std'],\n", "    'receita_total': ['mean', 'std'],\n", "    'ticket_medio': ['mean', 'std'],\n", "    'num_clientes_unicos': ['mean', 'std'],\n", "    'num_lojas': ['mean', 'std'],\n", "    'regiao': lambda x: x.mode().iloc[0] if not x.mode().empty else 'Mista'\n", "}).round(2)\n", "\n", "# Flatten column names\n", "cluster_analysis.columns = ['_'.join(col).strip() if col[1] else col[0] for col in cluster_analysis.columns]\n", "cluster_analysis = cluster_analysis.rename(columns={\n", "    'Dim_Lojas.Cidade_Emp_count': 'num_cidades',\n", "    'regiao_<lambda>': 'regiao_predominante'\n", "})\n", "\n", "print(\"\\nCaracterísticas dos clusters:\")\n", "display(cluster_analysis)\n", "\n", "# Identificar cidades em cada cluster\n", "print(\"\\nExemplos de cidades por cluster:\")\n", "for cluster_id in sorted(city_features['cluster'].unique()):\n", "    cluster_cities = city_features[city_features['cluster'] == cluster_id]\n", "    top_cities = cluster_cities.nlargest(5, 'receita_total')[['Dim_Lojas.Cidade_Emp', 'Dim_Lojas.Estado_Emp', 'receita_total']]\n", "    print(f\"\\nCluster {cluster_id} (Top 5 cidades por receita):\")\n", "    for _, city in top_cities.iterrows():\n", "        print(f\"   • {city['Dim_Lojas.Cidade_Emp']}/{city['Dim_Lojas.Estado_Emp']} - R$ {city['receita_total']:,.2f}\")"]}, {"cell_type": "markdown", "id": "phase2-title", "metadata": {}, "source": ["## 4. FASE 2: Aprendizado Supervisionado - Predição de Performance\n", "\n", "### Conceitos de Regressão Aplicados:\n", "\n", "**Random Forest Regressor:** Algoritmo ensemble que combina múltiplas árvores de decisão para criar predições mais robustas e precisas. Excelente para capturar relações não-lineares e interações entre variáveis.\n", "\n", "**Gradient Boosting:** Técnica de ensemble que constrói modelos sequencialmente, onde cada novo modelo corrige os erros do anterior. Muito eficaz para problemas de regressão complexos.\n", "\n", "**Feature Engineering:** Utilizaremos os clusters identificados na Fase 1 como features adici<PERSON><PERSON>, criando um modelo híbrido que aproveita tanto padrões não supervisionados quanto supervisionados.\n", "\n", "**Cross-Validation:** Técnica de validação que divide os dados em múltiplos folds para avaliar a performance do modelo de forma mais robusta e evitar overfitting.\n", "\n", "**Objetivo de Negócio:** Predizer a performance de vendas de óculos de grau em diferentes cidades para orientar decisões de expansão da rede de óticas especializadas."]}, {"cell_type": "code", "execution_count": 620, "id": "60a7390e", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Novo target 'performance_score' criado com sucesso.\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>performance_score</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>count</th>\n", "      <td>70.000000</td>\n", "    </tr>\n", "    <tr>\n", "      <th>mean</th>\n", "      <td>0.413314</td>\n", "    </tr>\n", "    <tr>\n", "      <th>std</th>\n", "      <td>0.130172</td>\n", "    </tr>\n", "    <tr>\n", "      <th>min</th>\n", "      <td>0.121125</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25%</th>\n", "      <td>0.322302</td>\n", "    </tr>\n", "    <tr>\n", "      <th>50%</th>\n", "      <td>0.411967</td>\n", "    </tr>\n", "    <tr>\n", "      <th>75%</th>\n", "      <td>0.513967</td>\n", "    </tr>\n", "    <tr>\n", "      <th>max</th>\n", "      <td>0.665579</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["       performance_score\n", "count          70.000000\n", "mean            0.413314\n", "std             0.130172\n", "min             0.121125\n", "25%             0.322302\n", "50%             0.411967\n", "75%             0.513967\n", "max             0.665579"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# CORREÇÃO: Novo Performance Score sem vazamento de dados\n", "def create_robust_performance_target(city_data):\n", "    \"\"\"\n", "    Cria um target robusto baseado apenas em métricas de resultado final,\n", "    evitando componentes que serão usados como features.\n", "    \"\"\"\n", "    # Usar apenas métricas de resultado que não serão features\n", "    target_components = {\n", "        'revenue_per_transaction': city_data['receita_total'] / city_data['num_transacoes'],\n", "        'customer_retention': city_data['num_clientes_unicos'] / city_data['num_transacoes'], \n", "        'store_efficiency': city_data['receita_per_store'] / city_data['ticket_medio'],\n", "        'market_size': np.log1p(city_data['volume_total'])  # Log para reduzir impacto de outliers\n", "    }\n", "    \n", "    # Normalizar componentes\n", "    scaler = MinMaxScaler()\n", "    components_df = pd.DataFrame(target_components, index=city_data.index)\n", "    normalized = scaler.fit_transform(components_df)\n", "    norm_df = pd.DataFrame(normalized, columns=components_df.columns, index=city_data.index)\n", "    \n", "    # Score ponderado focado em eficiência e sustentabilidade\n", "    performance_score = (\n", "        0.35 * norm_df['revenue_per_transaction'] +  # Eficiência por transação\n", "        0.25 * norm_df['customer_retention'] +       # Qualidade do relacionamento\n", "        0.25 * norm_df['store_efficiency'] +         # Eficiência operacional\n", "        0.15 * norm_df['market_size']                # Potencial de mercado\n", "    )\n", "    \n", "    return performance_score\n", "\n", "# NOVAS FEATURES SEM VAZAMENTO\n", "def engineer_independent_features(city_features):\n", "    \"\"\"C<PERSON><PERSON> features independentes do target\"\"\"\n", "    \n", "    city_features = city_features.copy()\n", "    \n", "    # 1. Features demográficas e geográficas (aproximações)\n", "    city_features['market_maturity'] = city_features['num_lojas'] / np.log1p(city_features['num_transacoes'])\n", "    city_features['customer_base_size'] = np.log1p(city_features['num_clientes_unicos'])\n", "    \n", "    # 2. Features de estabilidade de mercado\n", "    city_features['price_consistency'] = 1 / (1 + city_features['variabilidade_ticket'] / city_features['ticket_medio'])\n", "    city_features['transaction_frequency'] = city_features['num_transacoes'] / city_features['num_clientes_unicos']\n", "    \n", "    # 3. Features de saturação e competição (aproximadas)\n", "    city_features['market_density'] = city_features['num_lojas'] * city_features['ticket_medio'] / 1000\n", "    city_features['growth_potential'] = (city_features['volume_total'] / city_features['num_lojas']) / city_features['ticket_medio']\n", "    \n", "    # 4. Features econômicas regionais (proxy)\n", "    region_stats = city_features.groupby('regiao')['ticket_medio'].agg(['mean', 'std']).reset_index()\n", "    region_stats.columns = ['regiao', 'regional_avg_ticket', 'regional_ticket_std']\n", "    city_features = city_features.merge(region_stats, on='regiao')\n", "    \n", "    city_features['regional_performance'] = city_features['ticket_medio'] / city_features['regional_avg_ticket']\n", "    city_features['regional_stability'] = city_features['regional_ticket_std'] / city_features['regional_avg_ticket']\n", "    \n", "    # 5. Features de cluster (categórica -> dummies)\n", "    cluster_dummies = pd.get_dummies(city_features['cluster'], prefix='cluster')\n", "    city_features = pd.concat([city_features, cluster_dummies], axis=1)\n", "    \n", "    return city_features\n", "\n", "# Aplicar novo target e features\n", "city_features_enhanced = city_features.copy()\n", "city_features_enhanced['performance_score'] = create_robust_performance_target(city_features_enhanced)\n", "city_features_enhanced = engineer_independent_features(city_features_enhanced)\n", "\n", "print(\"Novo target 'performance_score' criado com sucesso.\")\n", "display(city_features_enhanced[['performance_score']].describe())"]}, {"cell_type": "code", "execution_count": 621, "id": "25573767", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["   Features selecionadas: ['market_maturity', 'customer_base_size', 'price_consistency', 'transaction_frequency', 'market_density', 'growth_potential', 'regional_performance', 'regional_stability', 'cluster_0', 'cluster_1', 'cluster_2', 'cluster_3', 'num_lojas']\n", "   Target: performance_score\n", "   Dataset limpo: 70 cidades × 13 features\n", "   Removidos: 0 registros com NaN)\n", "\n", "Correlações feature-target:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>feature</th>\n", "      <th>corr</th>\n", "      <th>abs_corr</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>customer_base_size</td>\n", "      <td>0.780751</td>\n", "      <td>0.780751</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>regional_performance</td>\n", "      <td>0.767634</td>\n", "      <td>0.767634</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>cluster_0</td>\n", "      <td>-0.693634</td>\n", "      <td>0.693634</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>cluster_1</td>\n", "      <td>0.607650</td>\n", "      <td>0.607650</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>market_density</td>\n", "      <td>0.499954</td>\n", "      <td>0.499954</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>transaction_frequency</td>\n", "      <td>-0.417900</td>\n", "      <td>0.417900</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>price_consistency</td>\n", "      <td>-0.331159</td>\n", "      <td>0.331159</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>num_lojas</td>\n", "      <td>0.236856</td>\n", "      <td>0.236856</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>cluster_2</td>\n", "      <td>0.210937</td>\n", "      <td>0.210937</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>regional_stability</td>\n", "      <td>0.116510</td>\n", "      <td>0.116510</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>market_maturity</td>\n", "      <td>0.061992</td>\n", "      <td>0.061992</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>cluster_3</td>\n", "      <td>-0.025764</td>\n", "      <td>0.025764</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>growth_potential</td>\n", "      <td>-0.007032</td>\n", "      <td>0.007032</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                  feature      corr  abs_corr\n", "1      customer_base_size  0.780751  0.780751\n", "6    regional_performance  0.767634  0.767634\n", "8               cluster_0 -0.693634  0.693634\n", "9               cluster_1  0.607650  0.607650\n", "4          market_density  0.499954  0.499954\n", "3   transaction_frequency -0.417900  0.417900\n", "2       price_consistency -0.331159  0.331159\n", "12              num_lojas  0.236856  0.236856\n", "10              cluster_2  0.210937  0.210937\n", "7      regional_stability  0.116510  0.116510\n", "0         market_maturity  0.061992  0.061992\n", "11              cluster_3 -0.025764  0.025764\n", "5        growth_potential -0.007032  0.007032"]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["Avaliacao de classificacao Ridge (Stable):\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.89      0.89      0.89         9\n", "           1       0.89      0.89      0.89         9\n", "\n", "    accuracy                           0.89        18\n", "   macro avg       0.89      0.89      0.89        18\n", "weighted avg       0.89      0.89      0.89        18\n", "\n", "AUC Score: 0.9877\n", "   Ridge (Stable): R² Test=0.968 | R² CV=0.939 ± 0.038 | Overfit=0.004\n", "Avaliacao de classificacao Gradient Boosting:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.89      0.89      0.89         9\n", "           1       0.89      0.89      0.89         9\n", "\n", "    accuracy                           0.89        18\n", "   macro avg       0.89      0.89      0.89        18\n", "weighted avg       0.89      0.89      0.89        18\n", "\n", "AUC Score: 0.9383\n", "   Gradient Boosting: R² Test=0.876 | R² CV=0.781 ± 0.182 | Overfit=0.124\n", "Avaliacao de classificacao Random Forest:\n", "              precision    recall  f1-score   support\n", "\n", "           0       0.88      0.78      0.82         9\n", "           1       0.80      0.89      0.84         9\n", "\n", "    accuracy                           0.83        18\n", "   macro avg       0.84      0.83      0.83        18\n", "weighted avg       0.84      0.83      0.83        18\n", "\n", "AUC Score: 0.9383\n", "   Random Forest: R² Test=0.786 | R² CV=0.810 ± 0.164 | Overfit=0.185\n", "\n", "SELEÇÃO DO MODELO FINAL:\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>r2_train</th>\n", "      <th>r2_test</th>\n", "      <th>r2_cv_mean</th>\n", "      <th>r2_cv_std</th>\n", "      <th>overfitting</th>\n", "      <th>estimator</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>Ridge (Stable)</th>\n", "      <td>0.971813</td>\n", "      <td>0.96757</td>\n", "      <td>0.939169</td>\n", "      <td>0.037673</td>\n", "      <td>0.004243</td>\n", "      <td>(StandardScaler(), Ridge(alpha=2.0))</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Grad<PERSON></th>\n", "      <td>0.999915</td>\n", "      <td>0.875749</td>\n", "      <td>0.781021</td>\n", "      <td>0.182404</td>\n", "      <td>0.124167</td>\n", "      <td>(([DecisionTreeRegressor(criterion='friedman_m...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>Random Forest</th>\n", "      <td>0.970689</td>\n", "      <td>0.785584</td>\n", "      <td>0.810226</td>\n", "      <td>0.164473</td>\n", "      <td>0.185104</td>\n", "      <td>((DecisionTreeRegressor(max_features=1.0, min_...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                   r2_train   r2_test r2_cv_mean r2_cv_std overfitting  \\\n", "Ridge (Stable)     0.971813   0.96757   0.939169  0.037673    0.004243   \n", "Gradient Boosting  0.999915  0.875749   0.781021  0.182404    0.124167   \n", "Random Forest      0.970689  0.785584   0.810226  0.164473    0.185104   \n", "\n", "                                                           estimator  \n", "Ridge (Stable)                  (StandardScaler(), Ridge(alpha=2.0))  \n", "Gradient Boosting  (([DecisionTreeRegressor(criterion='friedman_m...  \n", "Random Forest      ((DecisionTreeRegressor(max_features=1.0, min_...  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "MODELO SELECIONADO: Gradient Boosting\n", "TOP 15 CIDADES PARA EXPANSÃO (por retorno predito):\n"]}, {"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>Dim_Lojas.Cidade_Emp</th>\n", "      <th>Dim_Lojas.Estado_Emp</th>\n", "      <th>predicted_return</th>\n", "      <th>ticket_medio</th>\n", "      <th>num_lojas</th>\n", "      <th>cluster</th>\n", "      <th>regiao</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>CAMPOS DOS GOYTACAZES</td>\n", "      <td>RJ</td>\n", "      <td>0.652231</td>\n", "      <td>616.74</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>53</th>\n", "      <td>SANTO ANDRÉ</td>\n", "      <td>SP</td>\n", "      <td>0.621346</td>\n", "      <td>495.37</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>35</th>\n", "      <td>MANAUS</td>\n", "      <td>AM</td>\n", "      <td>0.609036</td>\n", "      <td>461.73</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>NORTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>55</th>\n", "      <td>SAO JOSE DOS CAMPOS</td>\n", "      <td>SP</td>\n", "      <td>0.592810</td>\n", "      <td>420.26</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>54</th>\n", "      <td>SANTOS</td>\n", "      <td>SP</td>\n", "      <td>0.572107</td>\n", "      <td>534.82</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>65</th>\n", "      <td>UBERABA</td>\n", "      <td>MG</td>\n", "      <td>0.568167</td>\n", "      <td>572.51</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>CAXIAS DO SUL</td>\n", "      <td>RS</td>\n", "      <td>0.567363</td>\n", "      <td>388.84</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>52</th>\n", "      <td>SANTARÉM</td>\n", "      <td>PA</td>\n", "      <td>0.559996</td>\n", "      <td>517.59</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>NORTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>30</th>\n", "      <td>JUNDIAÍ</td>\n", "      <td>SP</td>\n", "      <td>0.555462</td>\n", "      <td>431.15</td>\n", "      <td>2</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>JUIZ DE FORA</td>\n", "      <td>MG</td>\n", "      <td>0.541102</td>\n", "      <td>533.50</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>JOINVILLE</td>\n", "      <td>SC</td>\n", "      <td>0.539981</td>\n", "      <td>411.77</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUL</td>\n", "    </tr>\n", "    <tr>\n", "      <th>46</th>\n", "      <td>PRAIA GRANDE</td>\n", "      <td>SP</td>\n", "      <td>0.539804</td>\n", "      <td>516.49</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>FORTALEZA</td>\n", "      <td>CE</td>\n", "      <td>0.532151</td>\n", "      <td>652.10</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>NORDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>69</th>\n", "      <td>VOTORANTIM</td>\n", "      <td>SP</td>\n", "      <td>0.530219</td>\n", "      <td>493.51</td>\n", "      <td>1</td>\n", "      <td>1</td>\n", "      <td>SUDESTE</td>\n", "    </tr>\n", "    <tr>\n", "      <th>31</th>\n", "      <td>LAGES</td>\n", "      <td>SC</td>\n", "      <td>0.525784</td>\n", "      <td>527.47</td>\n", "      <td>2</td>\n", "      <td>2</td>\n", "      <td>SUL</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["     Dim_Lojas.Cidade_Emp Dim_Lojas.Estado_Emp  predicted_return  \\\n", "11  CAMPOS DOS GOYTACAZES                   RJ          0.652231   \n", "53            SANTO ANDRÉ                   SP          0.621346   \n", "35                 MANAUS                   AM          0.609036   \n", "55    SAO JOSE DOS CAMPOS                   SP          0.592810   \n", "54                 SANTOS                   SP          0.572107   \n", "65                UBERABA                   MG          0.568167   \n", "15          CAXIAS DO SUL                   RS          0.567363   \n", "52               SANTARÉM                   PA          0.559996   \n", "30                JUNDIAÍ                   SP          0.555462   \n", "29           JUIZ DE FORA                   MG          0.541102   \n", "27              JOINVILLE                   SC          0.539981   \n", "46           PRAIA GRANDE                   SP          0.539804   \n", "23              FORTALEZA                   CE          0.532151   \n", "69             VOTORANTIM                   SP          0.530219   \n", "31                  LAGES                   SC          0.525784   \n", "\n", "    ticket_medio  num_lojas  cluster    regiao  \n", "11        616.74          1        1   SUDESTE  \n", "53        495.37          2        1   SUDESTE  \n", "35        461.73          2        1     NORTE  \n", "55        420.26          2        1   SUDESTE  \n", "54        534.82          2        1   SUDESTE  \n", "65        572.51          1        1   SUDESTE  \n", "15        388.84          1        1       SUL  \n", "52        517.59          1        1     NORTE  \n", "30        431.15          2        1   SUDESTE  \n", "29        533.50          2        2   SUDESTE  \n", "27        411.77          1        1       SUL  \n", "46        516.49          2        2   SUDESTE  \n", "23        652.10          2        2  NORDESTE  \n", "69        493.51          1        1   SUDESTE  \n", "31        527.47          2        2       SUL  "]}, "metadata": {}, "output_type": "display_data"}, {"name": "stdout", "output_type": "stream", "text": ["\n", "CATEGORIZAÇÃO PARA INVESTIMENTO:\n", "   BAIXA PRIORIDADE - Retorno baixo: 42 cidades\n", "   CONSIDERÁVEL - Retorno moderado: 14 cidades\n", "   ALTA PRIORIDADE - Alto retorno + Alto ticket: 14 cidades\n"]}], "source": ["# ETAPA 1: Features para predição de retorno de novas lojas\n", "# Features independentes do target de performance\n", "expansion_features = [\n", "    'market_maturity',\n", "    'customer_base_size', \n", "    'price_consistency',\n", "    'transaction_frequency',\n", "    'market_density',\n", "    'growth_potential',\n", "    'regional_performance',\n", "    'regional_stability',\n", "    'cluster_0', 'cluster_1', 'cluster_2', 'cluster_3', \n", "    'num_lojas'  \n", "]\n", "\n", "# Evitar features que são componentes diretos da receita\n", "\n", "X_expansion = city_features_enhanced[expansion_features].replace([np.inf, -np.inf], np.nan)\n", "y_expansion = city_features_enhanced['performance_score'].copy()\n", "\n", "valid_mask = ~(y_expansion.isna() | X_expansion.isna().any(axis=1))\n", "X_expansion = X_expansion[valid_mask].copy()\n", "y_expansion = y_expansion[valid_mask].copy()\n", "\n", "print(f\"   Features selecionadas: {expansion_features}\")\n", "print(f\"   Target: performance_score\")\n", "print(f\"   Dataset limpo: {X_expansion.shape[0]} cidades × {X_expansion.shape[1]} features\")\n", "print(f\"   Removidos: {sum(~valid_mask)} registros com NaN)\")\n", "\n", "print(\"\\nCorrelações feature-target:\")\n", "corr_df = pd.DataFrame({\n", "    'feature': expansion_features,\n", "    'corr': [X_expansion[f].corr(y_expansion) for f in expansion_features]\n", "}).assign(abs_corr=lambda d: d['corr'].abs()).sort_values('abs_corr', ascending=False)\n", "display(corr_df)\n", "\n", "# ETAPA 2: Treinar modelo para predição de retorno\n", "# Divisão estratificada baseada em quartis do target (alinhar índices com X_selected)\n", "target_quartiles = pd.qcut(y_expansion, q=4, labels=[0,1,2,3])\n", "idx = X_expansion.index\n", "\n", "X_train_exp, X_test_exp, y_train_exp, y_test_exp = train_test_split(\n", "    X_expansion, y_expansion.loc[idx], test_size=0.25, random_state=42,\n", "    stratify=target_quartiles.loc[idx]\n", ")\n", "\n", "# Modelos otimizados para predição de retorno\n", "return_models = {\n", "    'Ridge (Stable)': Pipeline([('scaler', StandardScaler()), ('model', Ridge(alpha=2.0))]),\n", "    'Gradient Boosting': Pipeline([('model', GradientBoostingRegressor(n_estimators=200, max_depth=3, learning_rate=0.05, random_state=42))]),\n", "    'Random Forest': Pipeline([('model', RandomForestRegressor(n_estimators=400, max_depth=None, min_samples_split=4, random_state=42, n_jobs=-1))])\n", "}\n", "\n", "from sklearn.metrics import classification_report, roc_auc_score, confusion_matrix, ConfusionMatrixDisplay\n", "\n", "return_results = {}\n", "for name, model in return_models.items():\n", "    if isinstance(model, Pipeline):\n", "        pipe = model\n", "        pipe.fit(X_train_exp, y_train_exp)\n", "        r2_train = pipe.score(X_train_exp, y_train_exp)\n", "        r2_test = pipe.score(X_test_exp, y_test_exp)\n", "        cv_scores = cross_val_score(pipe, X_train_exp, y_train_exp, cv=5, scoring='r2')\n", "        final_estimator = pipe\n", "    else:\n", "        model.fit(X_train_exp, y_train_exp)\n", "        r2_train = model.score(X_train_exp, y_train_exp)\n", "        r2_test = model.score(X_test_exp, y_test_exp)\n", "        cv_scores = cross_val_score(model, X_train_exp, y_train_exp, cv=5, scoring='r2')\n", "        final_estimator = model\n", "        \n", "    # --- 2. Avaliação como CLASSIFICADOR ---\n", "    # Converter as probabilidades em classes (0 ou 1) usando um limiar de 0.5\n", "    # Para o AUC, usamos as probabilidades previstas diretamente\n", "    \n", "    y_test_binary = (y_test_exp > y_test_exp.median()).astype(int)\n", "    y_pred_proba = model.predict(X_test_exp)\n", "    y_pred_binary = (y_pred_proba > y_test_exp.median()).astype(int)\n", "\n", "    # Calculate AUC score with binary labels\n", "    auc_score = roc_auc_score(y_test_binary, y_pred_proba)\n", "\n", "    print(f'Avaliacao de classificacao {name}:')\n", "    print(classification_report(y_test_binary, y_pred_binary))\n", "    print(f\"AUC Score: {auc_score:.4f}\")\n", "    \n", "    return_results[name] = {\n", "        'r2_train': r2_train,\n", "        'r2_test': r2_test,\n", "        'r2_cv_mean': cv_scores.mean(),\n", "        'r2_cv_std': cv_scores.std(),\n", "        'overfitting': r2_train - r2_test,\n", "        'estimator': final_estimator\n", "    }\n", "    \n", "    print(f\"   {name}: R² Test={r2_test:.3f} | R² CV={cv_scores.mean():.3f} ± {cv_scores.std():.3f} | Overfit={r2_train - r2_test:.3f}\")\n", "\n", "print(f\"\\nSELEÇÃO DO MODELO FINAL:\")\n", "results_df_exp = pd.DataFrame(return_results).T.round(3)\n", "display(results_df_exp)\n", "\n", "best_name = '<PERSON><PERSON><PERSON>'\n", "best_model = return_results[best_name]['estimator']\n", "print(f\"\\nMODELO SELECIONADO: {best_name}\")\n", "\n", "# 7) Predições e ranking usando X_selected (mesmas colunas do treino)\n", "predicted_returns = best_model.predict(X_expansion)\n", "city_results = city_features_enhanced.loc[idx].copy()\n", "city_results['predicted_return'] = predicted_returns\n", "\n", "expansion_ranking = city_results.nlargest(15, 'predicted_return')[[\n", "    'Dim_Lojas.Cidade_Emp', 'Dim_Lojas.Estado_Emp', 'predicted_return',\n", "    'ticket_medio', 'num_lojas', 'cluster', 'regiao'\n", "]]\n", "print(\"TOP 15 CIDADES PARA EXPANSÃO (por retorno predito):\")\n", "display(expansion_ranking)\n", "\n", "# Segmentação para decisão\n", "def categorize_investment_priority(predicted_return, ticket_medio):\n", "    \"\"\"Categorizar prioridade de investimento\"\"\"\n", "    if predicted_return >= np.percentile(predicted_returns, 80):\n", "        if ticket_medio >= np.median(city_results['ticket_medio']):\n", "            return \"ALTA PRIORIDADE - Alto retorno + Alto ticket\"\n", "        else:\n", "            return \"MÉDIA PRIORIDADE - Alto retorno + Ticket moderado\"\n", "    elif predicted_return >= np.percentile(predicted_returns, 60):\n", "        return \"CONSIDERÁVEL - Retorno moderado\"\n", "    else:\n", "        return \"BAIXA PRIORIDADE - Retorno baixo\"\n", "\n", "city_results['investment_priority'] = city_results.apply(\n", "    lambda row: categorize_investment_priority(row['predicted_return'], row['ticket_medio']), \n", "    axis=1\n", ")\n", "\n", "priority_summary = city_results['investment_priority'].value_counts()\n", "print(f\"\\nCATEGORIZAÇÃO PARA INVESTIMENTO:\")\n", "for category, count in priority_summary.items():\n", "    print(f\"   {category}: {count} cidades\")"]}, {"cell_type": "code", "execution_count": 622, "id": "976d6a1f", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 1800x700 with 2 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from sklearn.metrics import confusion_matrix, ConfusionMatrixDisplay\n", "\n", "y_test_scores = best_model.predict(X_test_exp)\n", "threshold = y_test_exp.median()\n", "y_true_bin = (y_test_exp > threshold).astype(int)\n", "y_pred_bin = (y_test_scores > threshold).astype(int)\n", "cm = confusion_matrix(y_true_bin, y_pred_bin, labels=[0, 1])\n", "tn, fp, fn, tp = cm.ravel()\n", "\n", "# Criar figura com espaçamento adequado\n", "fig = plt.figure(figsize=(18, 7))\n", "gs = fig.add_gridspec(2, 4, height_ratios=[3, 1], width_ratios=[1, 0.3, 1, 1], wspace=0.4, hspace=0.3)\n", "\n", "# <PERSON>riz absoluta\n", "ax1 = fig.add_subplot(gs[0, 0])\n", "disp1 = ConfusionMatrixDisplay(\n", "    confusion_matrix=cm, \n", "    display_labels=['NÃO INVESTIR\\n(Baixo Potencial)', 'INVESTIR\\n(Alto Potencial)']\n", ")\n", "disp1.plot(ax=ax1, colorbar=False, cmap='Blues', values_format='d')\n", "ax1.set_title('<PERSON><PERSON> - Números Absolutos\\n(Quantidade de Cidades)', \n", "              fontsize=14, fontweight='bold', pad=20)\n", "ax1.set_xlabel('PREVISÃO DO MODELO', fontweight='bold', fontsize=12)\n", "ax1.set_ylabel('REALIDADE (Performance Real)', fontweight='bold', fontsize=12)\n", "\n", "# Limpar os textos automáticos e adicionar textos personalizados SEM SOBREPOSIÇÃO\n", "for text in ax1.texts:\n", "    text.remove()\n", "\n", "# Posicionar textos de forma que não se sobreponham\n", "ax1.text(0, 0, f'{tn}', ha='center', va='center', fontsize=20, fontweight='bold', color='white')\n", "ax1.text(1, 0, f'{fp}', ha='center', va='center', fontsize=20, fontweight='bold', color='black')\n", "ax1.text(0, 1, f'{fn}', ha='center', va='center', fontsize=20, fontweight='bold', color='black')\n", "ax1.text(1, 1, f'{tp}', ha='center', va='center', fontsize=20, fontweight='bold', color='white')\n", "\n", "# Matriz normalizada (com espaçamento)\n", "ax2 = fig.add_subplot(gs[0, 2])  # Mudança aqui: de gs[0, 1] para gs[0, 2]\n", "cm_norm = confusion_matrix(y_true_bin, y_pred_bin, labels=[0, 1], normalize='true')\n", "disp2 = ConfusionMatrixDisplay(\n", "    confusion_matrix=cm_norm, \n", "    display_labels=['NÃO INVESTIR\\n(Baixo Potencial)', 'INVESTIR\\n(Alto Potencial)']\n", ")\n", "disp2.plot(ax=ax2, colorbar=False, values_format='.0%', cmap='Oranges')\n", "ax2.set_title('<PERSON><PERSON> - Porcentagens\\n(% de Acerto por Categoria)', \n", "              fontsize=14, fontweight='bold', pad=20)\n", "ax2.set_xlabel('PREVISÃO DO MODELO', fontweight='bold', fontsize=12)\n", "ax2.set_ylabel('', fontsize=12)  # Remove ylabel duplicado\n", "\n", "for text in ax2.texts:\n", "    text.remove()\n", "\n", "# Posicionar textos da segunda matriz com fontsize maior\n", "tn_pct = cm_norm[0, 0]\n", "fp_pct = cm_norm[0, 1] \n", "fn_pct = cm_norm[1, 0]\n", "tp_pct = cm_norm[1, 1]\n", "\n", "ax2.text(0, 0, f'{tn_pct:.0%}', ha='center', va='center', fontsize=18, fontweight='bold', color='white')\n", "ax2.text(1, 0, f'{fp_pct:.0%}', ha='center', va='center', fontsize=18, fontweight='bold', color='black')\n", "ax2.text(0, 1, f'{fn_pct:.0%}', ha='center', va='center', fontsize=18, fontweight='bold', color='black')\n", "ax2.text(1, 1, f'{tp_pct:.0%}', ha='center', va='center', fontsize=18, fontweight='bold', color='white')\n", "\n", "plt.tight_layout()\n", "plt.show()"]}, {"cell_type": "code", "execution_count": 631, "id": "0fd9f857", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["MÉTRICAS FINAIS DO MODELO:\n", "• Accuracy: 88.9%\n", "• AUC: 0.938\n", "• R²: 0.876\n", "• R²: 0.781 ± 0.182\n", "• MSE: 0.003 ± 0.001\n"]}, {"data": {"image/png": "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********************************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", "text/plain": ["<Figure size 2400x1400 with 3 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["# Dados já disponíveis:\n", "# - best_model: <PERSON><PERSON> Boosting selecionado\n", "# - y_test_exp, y_train_exp: dados de teste e treino\n", "# - X_test_exp, X_train_exp: features de teste e treino\n", "\n", "from sklearn.metrics import accuracy_score, roc_auc_score\n", "from sklearn.model_selection import learning_curve\n", "\n", "# === 1. CALCULAR MÉTRICAS FINAIS ===\n", "y_test_scores = best_model.predict(X_test_exp)\n", "y_test_binary = (y_test_exp > y_test_exp.median()).astype(int)\n", "y_pred_binary = (y_test_scores > y_test_exp.median()).astype(int)\n", "\n", "# Métricas finais\n", "final_accuracy = accuracy_score(y_test_binary, y_pred_binary)\n", "final_auc = roc_auc_score(y_test_binary, y_test_scores)\n", "final_r2 = best_model.score(X_test_exp, y_test_exp)\n", "\n", "print(f\"MÉTRICAS FINAIS DO MODELO:\")\n", "print(f\"• Accuracy: {final_accuracy:.1%}\")\n", "print(f\"• AUC: {final_auc:.3f}\")\n", "print(f\"• R²: {final_r2:.3f}\")\n", "\n", "# === 2. VALIDAÇÃO CRUZADA ===\n", "def detailed_cross_validation(model, X, y, cv=5):\n", "    \"\"\"Validação cruzada detalhada com múltiplas métricas\"\"\"\n", "    r2_scores = cross_val_score(model, X, y, cv=cv, scoring='r2')\n", "    mse_scores = -cross_val_score(model, X, y, cv=cv, scoring='neg_mean_squared_error')\n", "    \n", "    # Para AUC e Accuracy, precisamos binarizar o target\n", "    y_binary = (y > y.median()).astype(int)\n", "    \n", "    return {\n", "        'R²': r2_scores,\n", "        'MSE': mse_scores,\n", "    }\n", "\n", "cv_results = detailed_cross_validation(best_model, X_train_exp, y_train_exp, cv=5)\n", "\n", "# Exibir estatísticas de validação cruzada\n", "for metric, scores in cv_results.items():\n", "    print(f\"• {metric}: {scores.mean():.3f} ± {scores.std():.3f}\")\n", "\n", "# === 3. <PERSON><PERSON><PERSON> DE APRENDIZADO ===\n", "train_sizes = np.linspace(0.1, 1.0, 10)\n", "\n", "train_sizes_abs, train_scores, val_scores = learning_curve(\n", "    best_model, X_train_exp, y_train_exp,\n", "    train_sizes=train_sizes,\n", "    cv=5,\n", "    scoring='r2',\n", "    n_jobs=-1,\n", "    random_state=42\n", ")\n", "\n", "# === 4. CRIAR VISUALIZAÇÕES MELHORADAS ===\n", "fig = plt.figure(figsize=(24, 14))\n", "# REDUZIR ESPAÇAMENTO: hspace de 0.35 para 0.20\n", "gs = fig.add_gridspec(3, 2, height_ratios=[1.2, 0.8, 1.2], width_ratios=[1, 1], \n", "                      hspace=0.12, wspace=0.18)\n", "\n", "# --- GRÁFICO 1: Curva de Aprendizado (SPAN 2 COLUMNS) ---\n", "ax1 = fig.add_subplot(gs[0, 0])\n", "\n", "train_mean = np.mean(train_scores, axis=1)\n", "train_std = np.std(train_scores, axis=1)\n", "val_mean = np.mean(val_scores, axis=1)\n", "val_std = np.std(val_scores, axis=1)\n", "\n", "# Melhorar cores e estilo\n", "ax1.plot(train_sizes_abs, train_mean, 'o-', color='#2E86AB', linewidth=4, \n", "         markersize=10, label='Score de Treino', markeredgecolor='white', markeredgewidth=2)\n", "ax1.fill_between(train_sizes_abs, train_mean - train_std, train_mean + train_std, \n", "                 alpha=0.25, color='#2E86AB')\n", "\n", "ax1.plot(train_sizes_abs, val_mean, 'o-', color='#F24236', linewidth=4, \n", "         markersize=10, label='Score de Validação', markeredgecolor='white', markeredgewidth=2)\n", "ax1.fill_between(train_sizes_abs, val_mean - val_std, val_mean + val_std, \n", "                 alpha=0.25, color='#F24236')\n", "\n", "ax1.set_xlabel('Número de Amostras de Treino', fontweight='bold', fontsize=16)\n", "ax1.set_ylabel('R² Score', fontweight='bold', fontsize=16)\n", "ax1.set_title('Curva de Aprendizado - Gradient Boosting Regressor', \n", "              fontsize=18, fontweight='bold', pad=30)\n", "ax1.legend(loc='lower right', fontsize=14, frameon=True, fancybox=True, shadow=True)\n", "ax1.grid(True, alpha=0.3, linestyle='--')\n", "\n", "# Adicionar informações de overfitting\n", "final_gap = train_mean[-1] - val_mean[-1]\n", "gap_text = f\"Gap Final: {final_gap:.3f}\"\n", "ax1.text(0.02, 0.98, gap_text, transform=ax1.transAxes,\n", "         bbox=dict(boxstyle=\"round,pad=0.3\", facecolor='lightblue', alpha=0.8),\n", "         fontsize=12, verticalalignment='top', fontweight='bold')\n", "\n", "# --- GRÁFICO 4: Predições vs Realidade (SPAN 2 COLUMNS) ---\n", "ax3 = fig.add_subplot(gs[0, 1])\n", "\n", "# Scatter plot melhorado\n", "scatter = ax3.scatter(y_test_exp, y_test_scores, alpha=0.7, s=100, \n", "                      c=y_test_scores, cmap='viridis', edgecolors='black', linewidth=1)\n", "\n", "# <PERSON><PERSON> perfeita (y = x)\n", "min_val = min(y_test_exp.min(), y_test_scores.min())\n", "max_val = max(y_test_exp.max(), y_test_scores.max())\n", "ax3.plot([min_val, max_val], [min_val, max_val], 'r--', linewidth=4, \n", "         label='Predição Perfeita (y=x)', alpha=0.8)\n", "\n", "# <PERSON><PERSON> de regressão\n", "from scipy import stats\n", "slope, intercept, r_value, p_value, std_err = stats.linregress(y_test_exp, y_test_scores)\n", "line = slope * y_test_exp + intercept\n", "ax3.plot(y_test_exp, line, 'g-', linewidth=4, \n", "         label=f'<PERSON><PERSON> (R²={r_value**2:.3f})', alpha=0.8)\n", "\n", "ax3.set_xlabel('Performance Real', fontweight='bold', fontsize=16)\n", "ax3.set_ylabel('Performance Predita', fontweight='bold', fontsize=16)\n", "ax3.set_title('Predições vs Realidade - Análise de Acurácia do Modelo', \n", "              fontsize=18, fontweight='bold', pad=30)\n", "ax3.legend(loc='upper left', fontsize=14, frameon=True, fancybox=True, shadow=True)\n", "ax3.grid(True, alpha=0.3, linestyle='--')\n", "\n", "# Colorbar para o scatter\n", "cbar = plt.colorbar(scatter, ax=ax3, shrink=0.8, aspect=30)\n", "cbar.set_label('Performance Predita', fontweight='bold', fontsize=12)\n", "\n", "# <PERSON><PERSON><PERSON>e de resíduos melhorada\n", "residuals = y_test_exp - y_test_scores\n", "mae = np.mean(np.abs(residuals))\n", "rmse = np.sqrt(np.mean(residuals**2))\n", "\n", "residual_stats = f\"\"\"ANÁLISE DE RESÍDUOS:\n", "Média: {np.mean(residuals):.4f}\n", "MAE: {mae:.4f}\n", "RMSE: {rmse:.4f}\n", "Std: {np.std(residuals):.4f}\n", "R² Real: {r_value**2:.3f}\"\"\"\n", "\n", "ax3.text(0.98, 0.02, residual_stats, transform=ax3.transAxes,\n", "         bbox=dict(boxstyle=\"round,pad=0.5\", facecolor='lightcyan', alpha=0.9),\n", "         fontsize=12, verticalalignment='bottom', horizontalalignment='right', \n", "         fontweight='bold')\n", "\n", "# Adicionar anotações de outliers se houver\n", "outliers_mask = np.abs(residuals) > 2 * np.std(residuals)\n", "if outliers_mask.any():\n", "    outlier_count = outliers_mask.sum()\n", "    ax3.text(0.02, 0.98, f'Outliers: {outlier_count}', transform=ax3.transAxes,\n", "             bbox=dict(boxstyle=\"round,pad=0.3\", facecolor='yellow', alpha=0.7),\n", "             fontsize=11, verticalalignment='top', fontweight='bold')\n", "\n", "# Melhorar layout final\n", "plt.suptitle('Análise Completa do Modelo Preditivo - Chilli Beans', \n", "             fontsize=20, fontweight='bold', y=0.98)\n", "plt.tight_layout()\n", "plt.subplots_adjust(top=0.9)\n", "plt.show()"]}, {"cell_type": "markdown", "id": "b7ec0f29", "metadata": {}, "source": ["# <PERSON><PERSON><PERSON>\n", "\n", "## <PERSON><PERSON>\n", "\n", "&ensp;Alguns dados poderiam ser inclusos para enriquecer o modelo, como estamos falando de cidades, poderiamos ter dados demográficos, geográficos, econômicos e de concorrência por cidade.\n", "\n", "**Demográficos**:\n", "- populacao_total\n", "- densidade_demografica\n", "- renda_per_capita\n", "- escolaridade_media\n", "\n", "**Econômicos**:\n", "- pib_municipal_per_capita\n", "- indice_gini\n", "- percentual_jovens_18_34\n", "- percentual_classe_media\n", "\n", "**Concorrência**:\n", "- num_oticas_concorrentes_5km\n", "- densidade_oticas_cidade\n", "- market_share_estimado\n", "- preco_medio_concorrencia\n", "- num_shopping_centers\n", "- fluxo_pedestres_estimado\n", "\n", "**Geográficos**:\n", "- distancia_capital_km\n", "- regiao_metropolitana\n", "- porte_cidade\n", "- cluster_economico_ibge\n", "- aeroporto_presente\n", "- universidades_count"]}, {"cell_type": "markdown", "id": "60b0b0d5", "metadata": {}, "source": ["## 6. Resultados e Conclusões\n", "\n", "### <PERSON><PERSON><PERSON>\n", "\n", "O modelo preditivo híbrido desenvolvido combina técnicas de aprendizado não supervisionado (clustering) e supervisionado (regressão + classificação) para apoiar decisões estratégicas de expansão da Chilli Beans.\n", "\n", "### Principais Conquistas:\n", "\n", "1. **Segmentação de Mercado**: Identificação de 4 clusters distintos de cidades com base em características de mercado\n", "2. **Modelo Preditivo**: Gradient Boosting com R² de ~0.6-0.7 para predição de performance\n", "3. **Classificação de Negócio**: Sistema inteligente para categorizar cidades como \"INVESTIR\" ou \"NÃO INVESTIR\"\n", "4. **<PERSON><PERSON> Contextualizada**: <PERSON><PERSON><PERSON><PERSON> com métricas relevantes ao negócio\n", "\n", "### Interpretação dos Resultados:\n", "\n", "- **Precisão**: Indica confiabilidade das recomendações de investimento\n", "- **Recall**: Capacidade de identificar todas as boas oportunidades  \n", "- **Especificidade**: Habilidade de evitar investimentos em locais inadequados\n", "- **Análise de Custos**: Quantificação do impacto financeiro de erros de classificação"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}